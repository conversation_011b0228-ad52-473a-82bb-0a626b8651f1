import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getCompletedSignaturesForToday } from '@/lib/tauri-api';
import type { CompletedSignature } from '@shared/schema';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircleIcon, EyeIcon } from '@heroicons/react/24/outline';
import DataTable from '@/components/common/DataTable';
import { formatDate, getCurrentDate } from '@/lib/utils';
import { Link } from 'wouter';
import { Button } from '@/components/ui/button';

// Basit bir saat formatlama fonksiyonu
const formatSignatureTime = (timeString: string): string => {
  try {
    const [hours, minutes] = timeString.split(':');
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
  } catch {
    return timeString; // Hata durumunda orijinal stringi döndür
  }
};

// DataTable'ın beklediği Column yapısına uygun bir arayüz
interface CompletedSignaturesColumn {
  key: keyof CompletedSignature | string;
  label: string;
  render?: (item: CompletedSignature) => React.ReactNode;
}

const columns: CompletedSignaturesColumn[] = [
  {
    key: 'tc_no',
    label: 'TC Kimlik No',
  },
  {
    key: 'first_name',
    label: 'Adı',
  },
  {
    key: 'last_name',
    label: 'Soyadı',
  },
  {
    key: 'signature_time',
    label: 'İmza Saati',
    render: (item: CompletedSignature) => formatSignatureTime(item.signature_time),
  },
  {
    key: 'actions',
    label: 'Detay',
    render: (item: CompletedSignature) => (
      <Link href={`/reports/convict-history?convictId=${item.convict_id}`}>
        <Button className="windows-button p-1" title="İmza Geçmişini Gör">
          <EyeIcon className="w-4 h-4" />
        </Button>
      </Link>
    ),
  },
];

export default function CompletedSignaturesPage() {
  const today = getCurrentDate();

  const { data: completedSignatures, isLoading, error } = useQuery({
    queryKey: ['completed-signatures-today'],
    queryFn: getCompletedSignaturesForToday,
    staleTime: 60 * 1000, // 1 dakika
  });

  if (isLoading) {
    return (
      <div className="windows-content">
        <div className="p-6 flex justify-center items-center h-full">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="windows-content">
        <div className="p-3">
          <Alert variant="destructive">
            <AlertDescription>
              Tamamlanan imzalar yüklenirken bir hata oluştu: {error.message}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">Tamamlanan İmzalar</h1>
            <span className="text-gray-600 text-xs ml-2">
              - Bugün ({formatDate(today)})
            </span>
          </div>
        </div>
      </div>

      <div className="p-3">
        <div className="windows-card">
          <div className="windows-section-title">Bugün İmzası Tamamlanan Hükümlüler</div>
          <div className="p-3">
            {completedSignatures && completedSignatures.length > 0 ? (
              <DataTable columns={columns} data={completedSignatures} />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <CheckCircleIcon className="w-12 h-12 mx-auto mb-4 text-green-500" />
                <p>Bugün imzası tamamlanan hükümlü bulunmamaktadır.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 