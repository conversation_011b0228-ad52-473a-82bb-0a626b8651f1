import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getAllExpectedSignaturesForToday } from '@/lib/tauri-api';
import type { ExpectedSignature } from '@shared/schema';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircleIcon, EyeIcon } from '@heroicons/react/24/outline';
import DataTable from '@/components/common/DataTable';
import { formatDate, getCurrentDate } from '@/lib/utils';
import { Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Basit bir saat formatlama fonksiyonu
const formatSignatureTime = (timeString: string): string => {
  try {
    const [hours, minutes] = timeString.split(':');
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
  } catch {
    return timeString; // Hata durumunda orijinal stringi döndür
  }
};

// DataTable'ın beklediği Column yapısına uygun bir arayüz
interface ExpectedSignaturesColumn {
  key: keyof ExpectedSignature | string;
  label: string;
  render?: (item: ExpectedSignature) => React.ReactNode;
}

const columns: ExpectedSignaturesColumn[] = [
  {
    key: 'tc_no',
    label: 'TC Kimlik No',
  },
  {
    key: 'first_name',
    label: 'Adı',
  },
  {
    key: 'last_name',
    label: 'Soyadı',
  },
  {
    key: 'status',
    label: 'Durum',
    render: (item: ExpectedSignature) => (
      <Badge 
        className={item.has_signed
          ? "bg-green-100 text-green-800 border-green-200 text-xs px-2 py-1 rounded" 
          : "bg-yellow-100 text-yellow-800 border-yellow-200 text-xs px-2 py-1 rounded"}
      >
        {item.has_signed ? `İmzalandı (${formatSignatureTime(item.signature_time!)})` : 'Bekliyor'}
      </Badge>
    ),
  },
  {
    key: 'actions',
    label: 'İşlemler',
    render: (item: ExpectedSignature) => (
      <div className="flex gap-1">
        {!item.has_signed && (
          <Link href={`/signatures/record?convictId=${item.convict_id}`}>
            <Button variant="windows-primary" size="sm">
              İmzala
            </Button>
          </Link>
        )}
        <Link href={`/reports/convict-history?convictId=${item.convict_id}`}>
          <Button variant="windows" size="sm" className="h-7 w-7 p-0" title="İmza Geçmişini Gör">
            <EyeIcon className="w-3 h-3" />
          </Button>
        </Link>
      </div>
    ),
  },
];

export default function ExpectedSignaturesPage() {
  const today = getCurrentDate();
  const { data: expectedSignatures, isLoading, error } = useQuery({
    queryKey: ['expected-signatures-today-page'],
    queryFn: getAllExpectedSignaturesForToday,
    staleTime: 60000,
  });

  if (isLoading) {
    return (
      <div className="windows-content flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-3 text-gray-600 text-sm">Beklenen imzalar yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="windows-content flex items-center justify-center">
        <div className="max-w-md p-4">
          <Alert variant="destructive" className="bg-red-50 border-red-200">
            <AlertDescription className="text-red-800 text-sm">
              Beklenen imzalar yüklenirken bir hata oluştu: {error.message}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="windows-content">
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="windows-title">
              Bugün Beklenen İmzalar
            </h1>
            <p className="windows-subtitle">
              {formatDate(today)} tarihinde imza atması beklenen hükümlüler
            </p>
            {expectedSignatures && (
              <div className="mt-2 flex items-center gap-4 text-xs text-gray-600">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Bekleyen: {expectedSignatures.filter(s => !s.has_signed).length}</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Tamamlanan: {expectedSignatures.filter(s => s.has_signed).length}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        <Card className="windows-card">
          <CardHeader>
            <CardTitle className="windows-section-title flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Bugün İmza Atması Beklenen Hükümlüler
              <span className="ml-2 bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">
                {formatDate(today)}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {expectedSignatures && expectedSignatures.length > 0 ? (
              <DataTable columns={columns} data={expectedSignatures} />
            ) : (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircleIcon className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-sm font-semibold text-gray-900 mb-1">Bugün İmza Beklenen Hükümlü Yok</h3>
                <p className="text-xs text-gray-600">Bugün imza atması beklenen hükümlü bulunmamaktadır.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 