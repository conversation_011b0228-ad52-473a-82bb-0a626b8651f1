import { Switch, Route } from "wouter";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "./components/ui/toaster";
import { TooltipProvider } from "./components/ui/tooltip";
import MainLayout from "./components/layout/MainLayout";
import LoginPage from "./features/auth/LoginPage";
import DashboardPage from "./features/dashboard/DashboardPage";
import ConvictListPage from "./features/convicts/ConvictListPage";
import AddConvictPage from "./features/convicts/AddConvictPage";
import EditConvictPage from "./features/convicts/EditConvictPage";
import ManageSignaturePeriodsPage from "./features/convicts/ManageSignaturePeriodsPage";
import ManageExemptionsPage from "./features/convicts/ManageExemptionsPage";
import SignatureFormPage from "./features/convicts/SignatureFormPage";
import SignatureFormSearchPage from "./features/convicts/SignatureFormSearchPage";
import ViolationRecordPage from "./features/convicts/ViolationRecordPage";
import RecordSignaturePage from "./features/signatures/RecordSignaturePage";
import ExpectedSignaturesPage from "./features/signatures/ExpectedSignaturesPage";
import CompletedSignaturesPage from "./features/signatures/CompletedSignaturesPage";
import DailyFollowUpPage from "./features/reports/DailyFollowUpPage";
import ConvictSignatureHistoryPage from "./features/reports/ConvictSignatureHistoryPage";
import ViolationReportPage from "./features/reports/ViolationReportPage";
import AllViolationsPage from "./features/reports/AllViolationsPage";
import UserListPage from "./features/admin/UserListPage";
import UsageGuidePage from "./features/help/UsageGuidePage";
import NotFound from "./pages/not-found";
import { useAuthStore } from "./store/authStore";
import { useRealtime } from "./hooks/useRealtime";
import { useTauriEvents } from "./hooks/useTauriEvents";
import * as React from "react";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

function ProtectedRoute({ component: Component }: { component: React.ComponentType }) {
  const { user } = useAuthStore();
  
  if (!user) {
    return <LoginPage />;
  }
  
  return (
    <MainLayout>
      <Component />
    </MainLayout>
  );
}

function AdminRoute({ component: Component }: { component: React.ComponentType }) {
  const { user } = useAuthStore();
  
  if (!user) {
    return <LoginPage />;
  }
  
  if (user.role !== 'ADMIN') {
    return <NotFound />;
  }
  
  return (
    <MainLayout>
      <Component />
    </MainLayout>
  );
}

function Router() {
  const { user } = useAuthStore();
  
  // Initialize real-time event listener inside QueryClientProvider context
  useRealtime();
  
  // Initialize Tauri event handlers for desktop app
  useTauriEvents();

  return (
    <Switch>
      <Route path="/login" component={LoginPage} />
      <Route path="/" component={() => user ? <ProtectedRoute component={DashboardPage} /> : <LoginPage />} />
      <Route path="/dashboard" component={() => <ProtectedRoute component={DashboardPage} />} />
      <Route path="/convicts" component={() => <ProtectedRoute component={ConvictListPage} />} />
      <Route path="/convicts/new" component={() => <ProtectedRoute component={AddConvictPage} />} />
      <Route path="/convicts/:id/edit" component={() => <ProtectedRoute component={EditConvictPage} />} />
      <Route path="/convicts/:id/periods" component={() => <ProtectedRoute component={ManageSignaturePeriodsPage} />} />
      <Route path="/convicts/:id/signature-form" component={() => <ProtectedRoute component={SignatureFormPage} />} />
      <Route path="/convicts/:id/violation-record" component={() => <ProtectedRoute component={ViolationRecordPage} />} />
      <Route path="/convicts/:id/exemptions" component={() => <ProtectedRoute component={ManageExemptionsPage} />} />
      <Route path="/signature-form/search" component={() => <ProtectedRoute component={SignatureFormSearchPage} />} />
      <Route path="/signatures/record" component={() => <ProtectedRoute component={RecordSignaturePage} />} />
      <Route path="/signatures/expected" component={() => <ProtectedRoute component={ExpectedSignaturesPage} />} />
      <Route path="/signatures/completed" component={() => <ProtectedRoute component={CompletedSignaturesPage} />} />
      <Route path="/reports/daily" component={() => <ProtectedRoute component={DailyFollowUpPage} />} />
      <Route path="/reports/convict-history" component={() => <ProtectedRoute component={ConvictSignatureHistoryPage} />} />
      <Route path="/reports/violations" component={() => <ProtectedRoute component={ViolationReportPage} />} />
      <Route path="/reports/all-violations" component={() => <ProtectedRoute component={AllViolationsPage} />} />
      <Route path="/admin/users" component={() => <AdminRoute component={UserListPage} />} />
      <Route path="/help/usage-guide" component={() => <ProtectedRoute component={UsageGuidePage} />} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <div className="windows-app font-segoe">
          <Toaster />
          <Router />
        </div>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;