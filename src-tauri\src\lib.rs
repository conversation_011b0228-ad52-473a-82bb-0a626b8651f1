mod database;
mod commands;

use database::init_database;
use commands::*;
use tauri::{
    Manager,
    menu::{MenuBuilder, MenuItemBuilder},
    tray::{MouseButton, MouseButtonState, TrayIconBuilder, TrayIconEvent},
};

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            println!("🚀 Starting EITS application setup...");

            let quit = MenuItemBuilder::with_id("quit", "Çıkış").build(app)?;
            let show = MenuItemBuilder::with_id("show", "Göster").build(app)?;
            let hide = MenuItemBuilder::with_id("hide", "Gizle").build(app)?;
            let minimize = MenuItemBuilder::with_id("minimize", "Küçült").build(app)?;
            let maximize = MenuItemBuilder::with_id("maximize", "Büyüt").build(app)?;
            let restore = MenuItemBuilder::with_id("restore", "Geri Yükle").build(app)?;
            let close = MenuItemBuilder::with_id("close", "Kapat").build(app)?;

            let menu = MenuBuilder::new(app)
                .items(&[&show, &hide, &minimize, &maximize, &restore, &close, &quit])
                .build()?;

            TrayIconBuilder::new()
                .menu(&menu)
                .on_menu_event(move |app, event| match event.id().as_ref() {
                    "quit" => {
                        app.exit(0);
                    }
                    "show" => {
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.show();
                        }
                    }
                    "hide" => {
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.hide();
                        }
                    }
                    "minimize" => {
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.minimize();
                        }
                    }
                    "maximize" => {
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.maximize();
                        }
                    }
                    "restore" => {
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.unminimize();
                        }
                    }
                    "close" => {
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.close();
                        }
                    }
                    _ => {}
                })
                .on_tray_icon_event(|tray, event| {
                    if let TrayIconEvent::Click {
                        button: MouseButton::Left,
                        button_state: MouseButtonState::Up,
                        ..
                    } = event
                    {
                        let app = tray.app_handle();
                        if let Some(window) = app.get_webview_window("main") {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                })
                .build(app)?;

            // Initialize the database using the same path as Drizzle
            println!("📁 Initializing database...");
            let db = match init_database() {
                Ok(db) => {
                    println!("✅ Database initialized successfully");
                    db
                }
                Err(e) => {
                    eprintln!("❌ Failed to initialize database: {}", e);
                    eprintln!("Database path attempted: {:?}", database::get_database_path());
                    return Err(format!("Database initialization failed: {}", e).into());
                }
            };

            // Validate that the database schema is compatible
            println!("🔍 Validating database schema...");
            match database::validate_database_schema(&db) {
                Ok(true) => println!("✅ Database schema validation passed"),
                Ok(false) => {
                    eprintln!("⚠️ Database schema validation failed - some tables might be missing");
                    eprintln!("Please run: npm run db:migrate");
                }
                Err(e) => eprintln!("⚠️ Database validation error: {}", e),
            }

            // Manage the database connection state
            app.manage(db);

            println!("🎉 EITS application setup completed successfully!");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            get_users,
            create_new_user,
            update_user_cmd,
            delete_user_cmd,
            find_user_by_username_cmd,
            get_convicts,
            get_convict_by_id_cmd,
            search_convict_by_tc,
            search_convicts_by_name,
            search_convicts_by_file_number,
            create_new_convict,
            bulk_create_convicts,
            bulk_upsert_convicts,
            update_convict_data,
            delete_convict_data,
            bulk_delete_convicts_data,
            get_dashboard_statistics,
            create_new_signature,
            bulk_create_signatures_with_transaction,
            delete_signature_data,
            get_convict_signatures,
            create_new_signature_period,
            update_signature_period_data,
            delete_signature_period_data,
            get_signature_periods,
            authenticate_user,
            validate_database,
            get_database_info,
            get_expected_signatures_for_today,
            get_violations_report,
            get_completed_signatures_for_today,
            get_all_expected_signatures_for_today,
            get_violations_for_last_30_days,
            save_binary_file,
            create_new_exemption,
            get_convict_exemptions,
            get_active_exemptions_for_date,
            update_exemption_data,
            delete_exemption_data,
            bulk_create_signature_periods_cmd,
            bulk_create_exemptions_cmd,
            get_convict_full_details_cmd,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
