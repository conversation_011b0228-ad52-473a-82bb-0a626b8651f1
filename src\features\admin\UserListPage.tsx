import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import DataTable from '@/components/common/DataTable';
import UserForm from './UserForm';
import { useToast } from '@/hooks/use-toast';
import { getUsers, createUser, updateUser, deleteUser } from '@/lib/tauri-api';
import { formatDateTime } from '@/lib/utils';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import type { User, InsertUser } from '@shared/schema';

export default function UserListPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  const { data: users, isLoading } = useQuery<User[]>({
    queryKey: ['users'],
    queryFn: getUsers,
  });

  const createUserMutation = useMutation({
    mutationFn: async (data: InsertUser) => {
      return await createUser(data);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı eklendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setIsDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Kullanıcı eklenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const updateUserMutation = useMutation({
    mutationFn: async (data: { id: number; user: Partial<InsertUser> }) => {
      return await updateUser(data.id, data.user);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı güncellendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setIsDialogOpen(false);
      setEditingUser(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Kullanıcı güncellenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const deleteUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      return await deleteUser(userId);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı silindi.',
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Kullanıcı silinirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: InsertUser) => {
    if (editingUser) {
      updateUserMutation.mutate({ id: editingUser.id, user: data });
    } else {
      createUserMutation.mutate(data);
    }
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setIsDialogOpen(true);
  };

  const handleDelete = (user: User) => {
    deleteUserMutation.mutate(user.id);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingUser(null);
  };

  const columns = [
    {
      key: 'username',
      label: 'Kullanıcı Adı',
    },
    {
      key: 'role',
      label: 'Rol',
      render: (user: User) => (
        <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded border ${
          user.role === 'ADMIN' 
            ? 'text-blue-700 bg-blue-100 border-blue-200' 
            : 'text-green-700 bg-green-100 border-green-200'
        }`}>
          {user.role === 'ADMIN' ? 'Yönetici' : 'Memur'}
        </span>
      ),
    },
    {
      key: 'isActive',
      label: 'Durum',
      render: (user: User) => (
        <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded border ${
          user.is_active 
            ? 'text-green-700 bg-green-100 border-green-200' 
            : 'text-gray-600 bg-gray-100 border-gray-200'
        }`}>
          {user.is_active ? 'Aktif' : 'Pasif'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      label: 'Oluşturma Tarihi',
      render: (user: User) => 
        user.created_at ? formatDateTime(user.created_at) : '-',
    },
  ];

  const actions = (user: User) => (
    <div className="flex items-center justify-end space-x-1">
      <Button
        onClick={() => handleEdit(user)}
        className="windows-button p-1"
      >
        <PencilIcon className="w-4 h-4" />
      </Button>
      
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button className="windows-button p-1 text-red-600 hover:bg-red-50">
            <TrashIcon className="w-4 h-4" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent className="windows-card">
          <AlertDialogHeader>
            <AlertDialogTitle className="windows-subtitle">Kullanıcıyı Sil</AlertDialogTitle>
            <AlertDialogDescription className="text-sm text-gray-600">
              "<span className="font-semibold">{user.username}</span>" kullanıcısını silmek istediğinizden emin misiniz? 
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="gap-2">
            <AlertDialogCancel className="windows-button">
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleDelete(user)}
              className="windows-button-primary bg-red-600 hover:bg-red-700"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">Kullanıcı Yönetimi</h1>
            <span className="text-gray-600 text-xs ml-2">
              - Sistem kullanıcılarını yönet
            </span>
          </div>
          <div className="flex items-center gap-4 text-xs text-gray-600">
            <span>Toplam: {users?.length || 0} kullanıcı</span>
            <span>Aktif: {users?.filter(u => u.is_active).length || 0}</span>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  onClick={() => setEditingUser(null)}
                  className="windows-button-primary"
                >
                  <PlusIcon className="w-4 h-4 mr-1" />
                  Yeni Kullanıcı
                </Button>
              </DialogTrigger>
              <DialogContent className="windows-card max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="windows-subtitle">
                    {editingUser ? 'Kullanıcı Düzenle' : 'Yeni Kullanıcı'}
                  </DialogTitle>
                </DialogHeader>
                <UserForm
                  initialData={editingUser ? {
                    username: editingUser.username,
                    role: editingUser.role,
                    isActive: editingUser.is_active,
                  } : undefined}
                  onSubmit={handleSubmit}
                  onCancel={handleDialogClose}
                  isLoading={createUserMutation.isPending || updateUserMutation.isPending}
                  submitLabel={editingUser ? 'Güncelle' : 'Ekle'}
                  isEdit={!!editingUser}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      <div className="p-3">
        <div className="windows-card">
          <div className="windows-section-title">Kullanıcı Listesi</div>
          <div className="p-3">
            <DataTable
              data={users || []}
              columns={columns}
              actions={actions}
              loading={isLoading}
              emptyMessage="Henüz kullanıcı eklenmemiş"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
