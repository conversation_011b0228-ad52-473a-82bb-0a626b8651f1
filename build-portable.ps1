param(
    [switch]$IncludeWebView2 = $false,
    [switch]$CreateZip = $false,
    [string]$OutputDir = "EITS-Portable"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "EITS Windows Portable Build Script" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Bu script EİTS uygulamasını portable hale getirir." -ForegroundColor Yellow
Write-Host "Parametreler:" -ForegroundColor White
Write-Host "  -IncludeWebView2  : WebView2 runtime'ı dahil et" -ForegroundColor Gray
Write-Host "  -CreateZip        : Zip dosyası oluştur" -ForegroundColor Gray
Write-Host "  -OutputDir        : Çıktı klasörü (varsayılan: EITS-Portable)" -ForegroundColor Gray
Write-Host ""

# MSVC toolchain kontrolü
try {
    $rustupOutput = rustup show 2>$null
    if ($rustupOutput -notmatch "msvc") {
        Write-Warning "MSVC toolchain not detected"
        Write-Host "Setting MSVC as default toolchain..." -ForegroundColor Yellow
        rustup default stable-x86_64-pc-windows-msvc
    }
} catch {
    Write-Error "Rustup not found. Please install Rust."
    exit 1
}

# Dependencies yükle
Write-Host "Installing dependencies..." -ForegroundColor Green
try {
    pnpm install
    if ($LASTEXITCODE -ne 0) { throw "pnpm install failed" }
} catch {
    Write-Error "Failed to install dependencies: $($_.Exception.Message)"
    exit 1
}

# Frontend build
Write-Host ""
Write-Host "Building frontend..." -ForegroundColor Green
try {
    npm run build
    if ($LASTEXITCODE -ne 0) { throw "Frontend build failed" }
} catch {
    Write-Error "Frontend build failed: $($_.Exception.Message)"
    exit 1
}

# Portable build
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Building Portable Version..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Push-Location "src-tauri"

try {
    Write-Host "Building executable only..." -ForegroundColor Green
    cargo tauri build --no-bundle --config tauri.nobundle.conf.json
    if ($LASTEXITCODE -ne 0) { throw "Portable build failed" }

    # Portable klasörü oluştur
    $portableDir = "..\$OutputDir"
    if (Test-Path $portableDir) {
        Remove-Item $portableDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $portableDir -Force | Out-Null    # Exe dosyasını kopyala
    $exePath = "target\release\eits.exe"
    if (-not (Test-Path $exePath)) {
        throw "Could not find executable file: $exePath"
    }
    
    # Exe dosyasını portable klasörüne kopyala ve yeniden adlandır
    Copy-Item $exePath "$portableDir\EITS-Portable.exe" -Force
    Write-Host "Executable copied to portable directory" -ForegroundColor Green

    # WebView2 runtime kopyala (eğer istenirse ve varsa)
    if ($IncludeWebView2 -and (Test-Path "Microsoft.WebView2.FixedVersionRuntime.137.0.3296.68.x64")) {
        Write-Host "Copying WebView2 runtime..." -ForegroundColor Yellow
        Copy-Item "Microsoft.WebView2.FixedVersionRuntime.137.0.3296.68.x64" "$portableDir\WebView2" -Recurse -Force
        Write-Host "WebView2 runtime included (~180MB)" -ForegroundColor Yellow
    }

    # Database kopyala
    if (Test-Path "database.sqlite") {
        Copy-Item "database.sqlite" "$portableDir\" -Force
        Write-Host "Database file copied" -ForegroundColor Green
    }

    # README oluştur
    $readmeContent = @"
EİTS - Elektronik İmza Takip Sistemi (Portable)
================================================

Bu portable versiyondur - kurulum gerektirmez.

Çalıştırmak için:
1. EITS-Portable.exe dosyasını çift tıklayın
2. Eğer WebView2 hatası alırsanız, Microsoft Edge'i güncelleyin

$(if ($IncludeWebView2) { "WebView2 Runtime: Dahil edildi" } else { "WebView2 Runtime: Sistem'den kullanılacak" })

Not: Bu versiyon ayarları kendi klasöründe saklar.

Versiyon: 0.1.0
Oluşturulma Tarihi: $(Get-Date -Format 'dd.MM.yyyy HH:mm:ss')
Build Parametreleri: IncludeWebView2=$IncludeWebView2, CreateZip=$CreateZip
"@
    
    Set-Content -Path "$portableDir\README.txt" -Value $readmeContent -Encoding UTF8

    # ZIP oluştur (eğer istenirse)
    if ($CreateZip) {
        Write-Host "Creating ZIP archive..." -ForegroundColor Yellow
        $zipPath = "..\$OutputDir.zip"
        if (Test-Path $zipPath) {
            Remove-Item $zipPath -Force
        }
        Compress-Archive -Path "$portableDir\*" -DestinationPath $zipPath -CompressionLevel Optimal
        Write-Host "ZIP archive created: $zipPath" -ForegroundColor Green
    }

} catch {
    Write-Error "Build failed: $($_.Exception.Message)"
    exit 1
} finally {
    Pop-Location
}

# Sonuç raporu
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Portable Build Completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$exeFile = Get-Item "$OutputDir\EITS-Portable.exe"
Write-Host "Portable files created in: $OutputDir" -ForegroundColor Cyan
Write-Host "Main executable: $($exeFile.FullName)" -ForegroundColor Cyan
Write-Host "Executable size: $([math]::Round($exeFile.Length / 1MB, 2)) MB" -ForegroundColor Cyan

if ($CreateZip) {
    $zipFile = Get-Item "$OutputDir.zip"
    Write-Host "ZIP archive: $($zipFile.FullName)" -ForegroundColor Cyan
    Write-Host "ZIP size: $([math]::Round($zipFile.Length / 1MB, 2)) MB" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Kullanım örnekleri:" -ForegroundColor Yellow
Write-Host "  - Portable klasörünü istediğiniz yere kopyalayabilirsiniz" -ForegroundColor Gray
Write-Host "  - USB stick'e kopyalayarak taşıyabilirsiniz" -ForegroundColor Gray
Write-Host "  - İnternet bağlantısı olmadan çalışır" -ForegroundColor Gray
Write-Host ""
