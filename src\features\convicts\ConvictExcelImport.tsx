import { useState, useCallback, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as XLSX from 'xlsx';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { bulkUpsertConvicts, bulkCreateSignaturePeriods, bulkCreateExemptions, saveBinaryFile } from '@/lib/tauri-api';
import { validateTcNo } from '@/lib/utils';
import { insertConvictSchema } from '@shared/schema';
import type { InsertConvict } from '@shared/schema';
import { save } from '@tauri-apps/plugin-dialog';
import {
  DocumentArrowUpIcon,
  DocumentArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

interface ImportError {
  row: number;
  field: string;
  message: string;
  value: any;
}

interface ParsedConvict extends InsertConvict {
  rowNumber: number;
  isValid: boolean;
  errors: ImportError[];
}

interface ParsedSignaturePeriod {
  convict_tc_no: string;
  start_date: string;
  end_date: string;
  frequency_type: 'WEEKLY' | 'X_DAYS' | 'MONTHLY_SPECIFIC';
  frequency_value: string;
  reference_date?: string;
  time_start?: string;
  time_end?: string;
  allowed_days?: string;
  is_active: boolean;
  rowNumber: number;
  isValid: boolean;
  errors: ImportError[];
}

interface ParsedExemption {
  convict_tc_no: string;
  exemption_type: 'LEAVE' | 'MEDICAL_REPORT';
  start_date: string;
  end_date: string;
  description?: string;
  document_path?: string;
  is_active: boolean;
  rowNumber: number;
  isValid: boolean;
  errors: ImportError[];
}

interface ImportResults {
  convicts: ParsedConvict[];
  signaturePeriods: ParsedSignaturePeriod[];
  exemptions: ParsedExemption[];
}

interface ConvictExcelImportProps {
  onImportComplete?: () => void;
}

export default function ConvictExcelImport({ onImportComplete }: ConvictExcelImportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<ImportResults>({
    convicts: [],
    signaturePeriods: [],
    exemptions: []
  });
  const [isParsingFile, setIsParsingFile] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();  // Bulk import mutation
  const bulkImportMutation = useMutation({
    mutationFn: async (data: { 
      convicts: InsertConvict[], 
      signaturePeriods: Omit<ParsedSignaturePeriod, 'rowNumber' | 'isValid' | 'errors'>[], 
      exemptions: Omit<ParsedExemption, 'rowNumber' | 'isValid' | 'errors'>[] 
    }) => {
      const { convicts, signaturePeriods, exemptions } = data;

      console.log('Mutation Başlangıcı:', {
        hükümlüSayısı: convicts.length,
        periyotSayısı: signaturePeriods.length,
        muafiyetSayısı: exemptions.length,
        periyotlar: signaturePeriods,
        muafiyetler: exemptions
      });

      const results = {
        convicts: [] as any[],
        signaturePeriods: [] as any[],
        exemptions: [] as any[],
        errors: [] as string[]
      };
      
      let currentStep = 0;
      const totalSteps = convicts.length + signaturePeriods.length + exemptions.length;
      
      try {
        // Import convicts first using upsert (bulk processing)
        let convictResults: any[] = [];
        if (convicts.length > 0) {
          try {
            convictResults = await bulkUpsertConvicts(convicts);
            console.log('Hükümlü Upsert Sonuçları:', convictResults);
            currentStep += convicts.length;
            setImportProgress(Math.round((currentStep / totalSteps) * 100));
          } catch (error) {
            results.errors.push(`Hükümlüler: ${error}`);
          }
        }
        results.convicts = convictResults;

        // Create a mapping of TC numbers to convict IDs
        const tcToIdMap = new Map<string, number>();
        convictResults.forEach(convict => {
          if (convict.tc_no && convict.id) {
            tcToIdMap.set(convict.tc_no, convict.id);
          }
        });

        console.log('TC No - ID Eşleşmeleri:', Object.fromEntries(tcToIdMap));

        // Import signature periods with convict_id
        if (signaturePeriods.length > 0) {
          try {
            // Her TC kimlik numarası için imza periyotlarını grupla
            const signaturePeriodsGroupedByTc = new Map<string, any[]>();
            signaturePeriods.forEach(period => {
              const existing = signaturePeriodsGroupedByTc.get(period.convict_tc_no) || [];
              signaturePeriodsGroupedByTc.set(period.convict_tc_no, [...existing, period]);
            });

            let allSignatureResults: any[] = [];

            // Her TC kimlik numarası için imza periyotlarını işle
            for (const [tcNo, periods] of signaturePeriodsGroupedByTc) {
              const convictId = tcToIdMap.get(tcNo);
              if (!convictId) {
                results.errors.push(`İmza periyodu için hükümlü bulunamadı: ${tcNo}`);
                continue;
              }

              // Her periyot için ayrı bir kayıt oluştur
              for (const period of periods) {
                const { convict_tc_no, ...periodData } = period;
                const periodWithId = {
                  ...periodData,
                  convict_id: convictId
                };

                console.log(`${tcNo} için işlenecek imza periyodu:`, periodWithId);

                try {
                  const signatureResults = await bulkCreateSignaturePeriods([periodWithId]);
                  console.log(`${tcNo} için imza periyodu sonuçları:`, signatureResults);
                  allSignatureResults = [...allSignatureResults, ...signatureResults];
                } catch (error) {
                  results.errors.push(`İmza periyodu oluşturma hatası (${tcNo}): ${error}`);
                }
              }
            }

            results.signaturePeriods = allSignatureResults;
            currentStep += signaturePeriods.length;
            setImportProgress(Math.round((currentStep / totalSteps) * 100));
          } catch (error) {
            results.errors.push(`İmza periyotları: ${error}`);
          }
        }

        // Import exemptions with convict_id
        if (exemptions.length > 0) {
          try {
            // Her TC kimlik numarası için muafiyetleri grupla
            const exemptionsGroupedByTc = new Map<string, any[]>();
            exemptions.forEach(exemption => {
              const existing = exemptionsGroupedByTc.get(exemption.convict_tc_no) || [];
              exemptionsGroupedByTc.set(exemption.convict_tc_no, [...existing, exemption]);
            });

            let allExemptionResults: any[] = [];

            // Her TC kimlik numarası için muafiyetleri işle
            for (const [tcNo, exemptionList] of exemptionsGroupedByTc) {
              const convictId = tcToIdMap.get(tcNo);
              if (!convictId) {
                results.errors.push(`Muafiyet için hükümlü bulunamadı: ${tcNo}`);
                continue;
              }

              // Her muafiyet için ayrı bir kayıt oluştur
              for (const exemption of exemptionList) {
                const { convict_tc_no, ...exemptionData } = exemption;
                const exemptionWithId = {
                  ...exemptionData,
                  convict_id: convictId
                };

                console.log(`${tcNo} için işlenecek muafiyet:`, exemptionWithId);

                try {
                  const exemptionResults = await bulkCreateExemptions([exemptionWithId]);
                  console.log(`${tcNo} için muafiyet sonuçları:`, exemptionResults);
                  allExemptionResults = [...allExemptionResults, ...exemptionResults];
                } catch (error) {
                  results.errors.push(`Muafiyet oluşturma hatası (${tcNo}): ${error}`);
                }
              }
            }

            results.exemptions = allExemptionResults;
            currentStep += exemptions.length;
            setImportProgress(Math.round((currentStep / totalSteps) * 100));
          } catch (error) {
            results.errors.push(`Muafiyetler: ${error}`);
          }
        }

        console.log('İçe Aktarma Sonuçları:', results);
        return results;
      } catch (error) {
        throw new Error(`İçe aktarma hatası: ${error}`);
      }
    },
    onSuccess: (results) => {
      const { convicts, signaturePeriods, exemptions, errors } = results;
      
      let successMessage = '';
      if (convicts.length > 0) successMessage += `${convicts.length} hükümlü kaydı işlendi`;
      if (signaturePeriods.length > 0) successMessage += `${successMessage ? ', ' : ''}${signaturePeriods.length} imza periyodu`;
      if (exemptions.length > 0) successMessage += `${successMessage ? ', ' : ''}${exemptions.length} muafiyet`;
      successMessage += ' başarıyla içe aktarıldı.';

      toast({
        title: 'İçe Aktarma Başarılı',
        description: successMessage,
      });

      if (errors.length > 0) {
        toast({
          title: 'Bazı Veriler İçe Aktarılamadı',
          description: `${errors.length} hata oluştu. Konsolu kontrol edin.`,
          variant: 'destructive',
        });
        console.error('İçe Aktarma Hataları:', errors);
      }

      queryClient.invalidateQueries({ queryKey: ['convicts'] });
      queryClient.invalidateQueries({ queryKey: ['signature-periods'] });
      queryClient.invalidateQueries({ queryKey: ['exemptions'] });
      
      // Invalidate specific exemption queries for each imported convict
      const importedConvictIds = new Set<number>();
      convicts.forEach(convict => {
        if (convict.id) {
          importedConvictIds.add(convict.id);
        }
      });
      
      // Also add convict IDs from exemptions that were created
      exemptions.forEach(exemption => {
        if (exemption.convict_id) {
          importedConvictIds.add(exemption.convict_id);
        }
      });
      
      // Invalidate exemption queries for each convict
      importedConvictIds.forEach(convictId => {
        queryClient.invalidateQueries({ queryKey: ['exemptions', convictId] });
      });
      
      setIsOpen(false);
      resetImportState();
      onImportComplete?.();
    },
    onError: (error) => {
      toast({
        title: 'İçe Aktarma Hatası',
        description: 'Veriler içe aktarılırken bir hata oluştu.',
        variant: 'destructive',
      });
      console.error('Bulk import error:', error);
    },
  });

  const resetImportState = () => {
    setSelectedFile(null);
    setParsedData({
      convicts: [],
      signaturePeriods: [],
      exemptions: []
    });
    setImportProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatExcelDate = (value: any): string => {
    if (!value) return '';
    
    // If it's already a date string, return it
    if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return value;
    }
    
    // If it's an Excel date number
    if (typeof value === 'number') {
      const date = XLSX.SSF.parse_date_code(value);
      if (date) {
        return `${date.y.toString().padStart(4, '0')}-${date.m.toString().padStart(2, '0')}-${date.d.toString().padStart(2, '0')}`;
      }
    }
    
    // Try to parse as date
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }
    
    return String(value);
  };

  const validateConvictData = (data: any, rowNumber: number): ParsedConvict => {
    const errors: ImportError[] = [];
    
    // Convert Excel data to convict format
    const convictData = {
      tc_no: String(data['TC Kimlik No'] || '').trim(),
      first_name: String(data['Ad'] || '').trim(),
      last_name: String(data['Soyad'] || '').trim(),
      phone_number: String(data['Hükümlü Telefon Numarası'] || '').trim() || undefined,
      relative_phone_number: String(data['Yakınına Ait Telefon Numarası'] || '').trim() || undefined,
      address: String(data['Adres Bilgileri'] || '').trim() || undefined,
      file_number: String(data['Dosya Numarası'] || '').trim() || undefined,
      supervision_start_date: data['Denetim Başlangıç Tarihi'] ? 
        formatExcelDate(data['Denetim Başlangıç Tarihi']) : '',
      supervision_end_date: data['Denetim Bitiş Tarihi'] ? 
        formatExcelDate(data['Denetim Bitiş Tarihi']) : '',
      is_active: data['Aktif'] !== false && data['Aktif'] !== 'Hayır' && data['Aktif'] !== 'HAYIR',
      notes: String(data['Notlar'] || '').trim() || undefined,
    };

    // Validate using Zod schema
    const result = insertConvictSchema.safeParse(convictData);
    
    if (!result.success) {
      result.error.errors.forEach(error => {
        errors.push({
          row: rowNumber,
          field: error.path.join('.'),
          message: error.message,
          value: error.path.reduce((obj: any, key) => obj?.[key], convictData),
        });
      });
    }

    // Additional TC No validation
    if (convictData.tc_no && !validateTcNo(convictData.tc_no)) {
      errors.push({
        row: rowNumber,
        field: 'tc_no',
        message: 'Geçersiz TC Kimlik No',
        value: convictData.tc_no,
      });
    }

    // Date validation
    if (convictData.supervision_start_date && convictData.supervision_end_date) {
      const startDate = new Date(convictData.supervision_start_date);
      const endDate = new Date(convictData.supervision_end_date);
      
      if (endDate <= startDate) {
        errors.push({
          row: rowNumber,
          field: 'supervision_end_date',
          message: 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır',
          value: convictData.supervision_end_date,
        });
      }
    }

    return {
      ...convictData,
      rowNumber,
      isValid: errors.length === 0,
      errors,
    };
  };

  const validateSignaturePeriodData = (data: any, rowNumber: number): ParsedSignaturePeriod | null => {
    const errors: ImportError[] = [];
    
    console.log(`[validateSignaturePeriodData - ${rowNumber}] Ham Veri:`, {
      tcNo: data['TC Kimlik No'],
      rawStartDate: data['İmza Periyodu Başlangıç'],
      rawEndDate: data['İmza Periyodu Bitiş'],
      rawFrequencyType: data['İmza Sıklığı Türü'],
      rawFrequencyValue: data['İmza Sıklığı Değeri'],
      rawReferenceDate: data['İmza Referans Tarihi']
    });
    
    const startDate = data['İmza Periyodu Başlangıç'] ? formatExcelDate(data['İmza Periyodu Başlangıç']) : '';
    const endDate = data['İmza Periyodu Bitiş'] ? formatExcelDate(data['İmza Periyodu Bitiş']) : '';
    const referenceDate = data['İmza Referans Tarihi'] ? formatExcelDate(data['İmza Referans Tarihi']) : undefined;

    console.log(`[validateSignaturePeriodData - ${rowNumber}] Formatlanmış Tarihler:`, {
      startDate,
      endDate,
      referenceDate
    });

    const frequencyTypeMapping: { [key: string]: 'WEEKLY' | 'X_DAYS' | 'MONTHLY_SPECIFIC' } = {
      'HAFTALIK': 'WEEKLY',
      'WEEKLY': 'WEEKLY',
      'GÜNLÜK': 'X_DAYS',
      'GUNLUK': 'X_DAYS',
      'X_DAYS': 'X_DAYS',
      'AYLIK': 'MONTHLY_SPECIFIC',
      'MONTHLY_SPECIFIC': 'MONTHLY_SPECIFIC',
      'AYLIK BELİRLİ GÜN': 'MONTHLY_SPECIFIC',
      'AYLIK BELIRLI GUN': 'MONTHLY_SPECIFIC'
    };

    const weekdayMapping: { [key: string]: string } = {
      'PAZARTESİ': 'MONDAY',
      'PAZARTESI': 'MONDAY',
      'SALI': 'TUESDAY',
      'ÇARŞAMBA': 'WEDNESDAY',
      'CARSAMBA': 'WEDNESDAY',
      'PERŞEMBE': 'THURSDAY',
      'PERSEMBE': 'THURSDAY',
      'CUMA': 'FRIDAY',
      'CUMARTESİ': 'SATURDAY',
      'CUMARTESI': 'SATURDAY',
      'PAZAR': 'SUNDAY'
    };
    
    const rawFrequencyTypeExcel = String(data['İmza Sıklığı Türü'] || '').trim().toUpperCase();
    const frequencyType = frequencyTypeMapping[rawFrequencyTypeExcel] || rawFrequencyTypeExcel as 'WEEKLY' | 'X_DAYS' | 'MONTHLY_SPECIFIC';
    
    let frequencyValueExcel = String(data['İmza Sıklığı Değeri'] || '').trim().toUpperCase();
    let processedFrequencyValue = frequencyValueExcel;
    if (frequencyType === 'WEEKLY') {
      if (frequencyValueExcel.includes(',')) {
        // Handle multiple days like "SALI, PERŞEMBE, CUMARTESİ"
        const days = frequencyValueExcel.split(',').map(d => d.trim().toUpperCase());
        const mappedDays = days.map(day => weekdayMapping[day] || day);
        processedFrequencyValue = mappedDays.join(',');
      } else {
        // Handle single day
        processedFrequencyValue = weekdayMapping[frequencyValueExcel] || frequencyValueExcel;
      }
    }

    console.log(`[validateSignaturePeriodData - ${rowNumber}] İşlenmiş Sıklık:`, {
      rawFrequencyTypeExcel,
      mappedFrequencyType: frequencyType,
      rawFrequencyValueExcel: frequencyValueExcel,
      processedFrequencyValue
    });

    // Zaman kısıtlamalarını işle
    const timeStart = String(data['İmza Başlangıç Saati'] || '').trim() || undefined;
    const timeEnd = String(data['İmza Bitiş Saati'] || '').trim() || undefined;
    const allowedDaysRaw = String(data['İzin Verilen Günler'] || '').trim();
    
    let allowedDays: string | undefined = undefined;
    if (allowedDaysRaw) {
      try {
        // Gün isimlerini normalize et ve JSON array'e çevir
        const dayMapping: { [key: string]: string } = {
          'pazartesi': 'monday',
          'salı': 'tuesday', 
          'çarşamba': 'wednesday',
          'perşembe': 'thursday',
          'cuma': 'friday',
          'cumartesi': 'saturday',
          'pazar': 'sunday',
          'monday': 'monday',
          'tuesday': 'tuesday',
          'wednesday': 'wednesday', 
          'thursday': 'thursday',
          'friday': 'friday',
          'saturday': 'saturday',
          'sunday': 'sunday'
        };
        
        const normalizedDays = allowedDaysRaw.split(',')
          .map(day => day.trim().toLowerCase())
          .map(day => dayMapping[day])
          .filter(day => day);
          
        if (normalizedDays.length > 0) {
          allowedDays = JSON.stringify(normalizedDays);
        }
      } catch (e) {
        console.log(`İzin verilen günler çevrilemedi (${rowNumber}):`, allowedDaysRaw, e);
      }
    }
    
    const periodData = {
      convict_tc_no: String(data['TC Kimlik No'] || '').trim(),
      start_date: startDate,
      end_date: endDate,
      frequency_type: frequencyType,
      frequency_value: processedFrequencyValue,
      reference_date: referenceDate,
      time_start: timeStart,
      time_end: timeEnd,
      allowed_days: allowedDays,
      is_active: data['İmza Periyodu Aktif'] !== false && data['İmza Periyodu Aktif'] !== 'Hayır' && data['İmza Periyodu Aktif'] !== 'HAYIR',
    };

    // Debug log - dönüştürülmüş veriyi göster
    console.log('Dönüştürülmüş İmza Periyodu Verisi:', {
      satır: rowNumber,
      tcNo: periodData.convict_tc_no,
      başlangıç: periodData.start_date,
      bitiş: periodData.end_date,
      sıklıkTürü: periodData.frequency_type,
      sıklıkDeğeri: periodData.frequency_value,
      referansTarihi: periodData.reference_date,
      zamanBaşlangıç: periodData.time_start,
      zamanBitiş: periodData.time_end,
      izinVerilenGünler: periodData.allowed_days,
      aktif: periodData.is_active
    });

    // TC kimlik numarası kontrolü
    if (!periodData.convict_tc_no) {
      console.log('TC Kimlik No eksik, satır atlanıyor:', rowNumber);
      return null;
    }

    // İmza periyodu verilerinin kontrolü - daha detaylı kontrol
    const hasStartDate = data['İmza Periyodu Başlangıç'] !== undefined && data['İmza Periyodu Başlangıç'] !== '';
    const hasEndDate = data['İmza Periyodu Bitiş'] !== undefined && data['İmza Periyodu Bitiş'] !== '';
    const hasFrequencyType = data['İmza Sıklığı Türü'] !== undefined && data['İmza Sıklığı Türü'] !== '';
    const hasFrequencyValue = data['İmza Sıklığı Değeri'] !== undefined && data['İmza Sıklığı Değeri'] !== '';

    const hasSignaturePeriodData = hasStartDate || hasEndDate || hasFrequencyType || hasFrequencyValue;
    
    console.log('İmza Periyodu Veri Kontrolü:', {
      satır: rowNumber,
      başlangıçVar: hasStartDate,
      bitişVar: hasEndDate,
      sıklıkTürüVar: hasFrequencyType,
      sıklıkDeğeriVar: hasFrequencyValue,
      periyotVerisiVar: hasSignaturePeriodData
    });

    if (!hasSignaturePeriodData) {
      console.log('İmza periyodu verisi yok, satır atlanıyor:', rowNumber);
      return null;
    }

    // Zorunlu alanların kontrolü
    if (hasSignaturePeriodData) {
      if (!periodData.start_date) {
        errors.push({
          row: rowNumber,
          field: 'start_date',
          message: 'İmza periyodu başlangıç tarihi gereklidir',
          value: periodData.start_date,
        });
      }

      if (!periodData.end_date) {
        errors.push({
          row: rowNumber,
          field: 'end_date',
          message: 'İmza periyodu bitiş tarihi gereklidir',
          value: periodData.end_date,
        });
      }

      if (!periodData.frequency_type) {
        errors.push({
          row: rowNumber,
          field: 'frequency_type',
          message: 'İmza sıklığı türü HAFTALIK (WEEKLY), GÜNLÜK (X_DAYS) veya AYLIK (MONTHLY_SPECIFIC) olmalıdır',
          value: rawFrequencyTypeExcel,
        });
      }

      if (!periodData.frequency_value) {
        errors.push({
          row: rowNumber,
          field: 'frequency_value',
          message: 'İmza sıklığı değeri gereklidir',
          value: periodData.frequency_value,
        });
      }
    }

    // Validate frequency type
    const validFrequencyTypes = ['WEEKLY', 'X_DAYS', 'MONTHLY_SPECIFIC'];
    if (periodData.frequency_type && !validFrequencyTypes.includes(periodData.frequency_type)) {
      errors.push({
        row: rowNumber,
        field: 'frequency_type',
        message: 'İmza sıklığı türü HAFTALIK (WEEKLY), GÜNLÜK (X_DAYS) veya AYLIK (MONTHLY_SPECIFIC) olmalıdır',
        value: rawFrequencyTypeExcel,
      });
    }

    // Validate frequency value based on type
    if (periodData.frequency_type === 'X_DAYS') {
      const dayValue = parseInt(periodData.frequency_value);
      if (isNaN(dayValue) || dayValue < 1 || dayValue > 30) {
        errors.push({
          row: rowNumber,
          field: 'frequency_value',
          message: 'X_DAYS için sıklık değeri 1-30 arası sayı olmalıdır',
          value: periodData.frequency_value,
        });
      }
    } else if (periodData.frequency_type === 'MONTHLY_SPECIFIC') {
      // MONTHLY_SPECIFIC can have single day (e.g., "15") or multiple days (e.g., "1,15,30")
      const dayValues = periodData.frequency_value.split(',').map(d => d.trim());
      let allDaysValid = true;
      const invalidDays = [];
      
      if (dayValues.length === 0 || dayValues.some(d => d === '')) {
        allDaysValid = false;
      } else {
        for (const dayStr of dayValues) {
          const dayValue = parseInt(dayStr);
          if (isNaN(dayValue) || dayValue < 1 || dayValue > 31) {
            allDaysValid = false;
            invalidDays.push(dayStr);
          }
        }
      }
      
      if (!allDaysValid) {
        errors.push({
          row: rowNumber,
          field: 'frequency_value',
          message: 'MONTHLY_SPECIFIC için sıklık değeri 1-31 arası gün numarası olmalıdır (örn: "15" veya "1,15,30")',
          value: periodData.frequency_value,
        });
      }
    } else if (periodData.frequency_type === 'WEEKLY') {
      const validWeekdays = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];
      // Değer boşsa veya null ise hata vermeden önce kontrol et
      if (!periodData.frequency_value) {
        errors.push({
          row: rowNumber,
          field: 'frequency_value',
          message: 'WEEKLY için sıklık değeri boş olamaz.',
          value: periodData.frequency_value,
        });
      } else {
        const days = periodData.frequency_value.split(',').map(d => d.trim().toUpperCase());
        let allDaysValid = true;
        if (days.length === 0 || days.some(d => d === '')) {
          allDaysValid = false; // Virgül var ama arada boş değer var ya da tamamen boş
        }
        for (const day of days) {
          if (day && !validWeekdays.includes(day)) { // Sadece doluysa kontrol et
            allDaysValid = false;
            break;
          }
        }
        if (!allDaysValid) {
          errors.push({
            row: rowNumber,
            field: 'frequency_value',
            message: 'WEEKLY için sıklık değeri geçerli gün adları (örn: MONDAY veya MONDAY,TUESDAY) içermelidir. Her gün adı geçerli olmalı ve boş olmamalıdır.',
            value: periodData.frequency_value,
          });
        }
      }
    }

    // Date validation
    if (periodData.start_date && periodData.end_date) {
      const startDate = new Date(periodData.start_date);
      const endDate = new Date(periodData.end_date);
      
      if (endDate <= startDate) {
        errors.push({
          row: rowNumber,
          field: 'end_date',
          message: 'İmza periyodu bitiş tarihi başlangıç tarihinden sonra olmalıdır',
          value: periodData.end_date,
        });
      }
    }

    // Time restrictions validation
    if (periodData.time_start && periodData.time_end) {
      // Validate time format (HH:MM)
      const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      
      if (!timePattern.test(periodData.time_start)) {
        errors.push({
          row: rowNumber,
          field: 'time_start',
          message: 'Başlangıç saati HH:MM formatında olmalıdır (örn: 10:00)',
          value: periodData.time_start,
        });
      }
      
      if (!timePattern.test(periodData.time_end)) {
        errors.push({
          row: rowNumber,
          field: 'time_end',
          message: 'Bitiş saati HH:MM formatında olmalıdır (örn: 22:00)',
          value: periodData.time_end,
        });
      }
      
      // Validate time range
      if (timePattern.test(periodData.time_start) && timePattern.test(periodData.time_end)) {
        const startTime = periodData.time_start.replace(':', '');
        const endTime = periodData.time_end.replace(':', '');
        
        if (startTime >= endTime) {
          errors.push({
            row: rowNumber,
            field: 'time_end',
            message: 'Bitiş saati başlangıç saatinden sonra olmalıdır',
            value: periodData.time_end,
          });
        }
      }
    }

    // Validate that both time_start and time_end are provided together
    if ((periodData.time_start && !periodData.time_end) || (!periodData.time_start && periodData.time_end)) {
      errors.push({
        row: rowNumber,
        field: 'time_restrictions',
        message: 'Zaman kısıtlaması kullanılacaksa hem başlangıç hem bitiş saati belirtilmelidir',
        value: `${periodData.time_start || ''} - ${periodData.time_end || ''}`,
      });
    }

    const isValid = errors.length === 0;
    console.log('İmza Periyodu Doğrulama Sonucu:', {
      satır: rowNumber,
      tcNo: periodData.convict_tc_no,
      geçerli: isValid,
      hatalar: errors,
      işlenecekVeri: isValid ? periodData : null
    });

    return {
      ...periodData,
      rowNumber,
      isValid,
      errors,
    };
  };

  const validateExemptionData = (data: any, rowNumber: number): ParsedExemption | null => {
    const errors: ImportError[] = [];
    
    // Convert Turkish exemption types to English
    const exemptionTypeMapping: { [key: string]: 'LEAVE' | 'MEDICAL_REPORT' } = {
      'İZİN': 'LEAVE',
      'IZIN': 'LEAVE',
      'LEAVE': 'LEAVE',
      'SAĞLIK RAPORU': 'MEDICAL_REPORT',
      'SAGLIK RAPORU': 'MEDICAL_REPORT',
      'MEDICAL_REPORT': 'MEDICAL_REPORT',
      'SAĞLIK': 'MEDICAL_REPORT',
      'SAGLIK': 'MEDICAL_REPORT'
    };
    
    const rawExemptionType = String(data['Muafiyet Türü'] || '').trim().toUpperCase();
    const exemptionType = exemptionTypeMapping[rawExemptionType] || rawExemptionType as 'LEAVE' | 'MEDICAL_REPORT';
    
    // Convert Excel data to exemption format
    const exemptionData = {
      convict_tc_no: String(data['TC Kimlik No'] || '').trim(),
      exemption_type: exemptionType,
      start_date: data['Muafiyet Başlangıç'] ? formatExcelDate(data['Muafiyet Başlangıç']) : '',
      end_date: data['Muafiyet Bitiş'] ? formatExcelDate(data['Muafiyet Bitiş']) : '',
      description: String(data['Muafiyet Açıklaması'] || '').trim() || undefined,
      document_path: String(data['Muafiyet Belgesi'] || '').trim() || undefined,
      is_active: data['Muafiyet Aktif'] !== false && data['Muafiyet Aktif'] !== 'Hayır' && data['Muafiyet Aktif'] !== 'HAYIR',
    };

    // TC kimlik numarası kontrolü
    if (!exemptionData.convict_tc_no) {
      return null;
    }

    // Muafiyet verilerinin kontrolü
    const hasExemptionData = exemptionData.exemption_type || exemptionData.start_date || exemptionData.end_date;
    if (!hasExemptionData) {
      return null;
    }

    // Zorunlu alanların kontrolü
    if (hasExemptionData) {
      if (!exemptionData.exemption_type) {
        errors.push({
          row: rowNumber,
          field: 'exemption_type',
          message: 'Muafiyet türü gereklidir',
          value: rawExemptionType,
        });
      }

      if (!exemptionData.start_date) {
        errors.push({
          row: rowNumber,
          field: 'start_date',
          message: 'Muafiyet başlangıç tarihi gereklidir',
          value: exemptionData.start_date,
        });
      }

      if (!exemptionData.end_date) {
        errors.push({
          row: rowNumber,
          field: 'end_date',
          message: 'Muafiyet bitiş tarihi gereklidir',
          value: exemptionData.end_date,
        });
      }
    }

    // Validate exemption type
    const validExemptionTypes = ['LEAVE', 'MEDICAL_REPORT'];
    if (exemptionData.exemption_type && !validExemptionTypes.includes(exemptionData.exemption_type)) {
      errors.push({
        row: rowNumber,
        field: 'exemption_type',
        message: 'Muafiyet türü İZİN (LEAVE) veya SAĞLIK RAPORU (MEDICAL_REPORT) olmalıdır',
        value: rawExemptionType,
      });
    }

    // Date validation
    if (exemptionData.start_date && exemptionData.end_date) {
      const startDate = new Date(exemptionData.start_date);
      const endDate = new Date(exemptionData.end_date);
      
      if (endDate <= startDate) {
        errors.push({
          row: rowNumber,
          field: 'end_date',
          message: 'Muafiyet bitiş tarihi başlangıç tarihinden sonra olmalıdır',
          value: exemptionData.end_date,
        });
      }
    }

    return {
      ...exemptionData,
      rowNumber,
      isValid: errors.length === 0,
      errors,
    };
  };

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);
    setIsParsingFile(true);
    setParsedData({
      convicts: [],
      signaturePeriods: [],
      exemptions: []
    });

    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // Convert to JSON with header row
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length < 2) {
        throw new Error('Excel dosyası en az 2 satır içermeli (başlık + veri)');
      }

      // Get headers from first row
      const headers = jsonData[0] as string[];
      
      // Check required headers
      const requiredHeaders = ['TC Kimlik No', 'Ad', 'Soyad', 'Denetim Başlangıç Tarihi', 'Denetim Bitiş Tarihi'];
      const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
      
      if (missingHeaders.length > 0) {
        throw new Error(`Eksik sütunlar: ${missingHeaders.join(', ')}`);
      }

      // Parse data rows
      const dataRows = jsonData.slice(1) as any[][];
      const parsedConvicts: ParsedConvict[] = [];
      const parsedSignaturePeriods: ParsedSignaturePeriod[] = [];
      const parsedExemptions: ParsedExemption[] = [];
      let totalRows = 0;
      let validConvictCount = 0;
      let validPeriodCount = 0;
      let validExemptionCount = 0;

      // TC kimlik numaralarına göre grupla
      const rowsByTcNo = new Map<string, any[]>();

      dataRows.forEach((row: any[], index) => {
        if (row.some(cell => cell !== undefined && cell !== '')) {
          totalRows++;
          const rowData: any = {};
          headers.forEach((header, headerIndex) => {
            rowData[header] = row[headerIndex];
          });

          const tcNo = String(rowData['TC Kimlik No'] || '').trim();
          if (tcNo) {
            const existingRows = rowsByTcNo.get(tcNo) || [];
            rowsByTcNo.set(tcNo, [...existingRows, { rowNumber: index + 2, data: rowData }]);
          }
        }
      });

      console.log('TC Kimlik Numaralarına Göre Satırlar:', Object.fromEntries(rowsByTcNo));

      // Her TC kimlik numarası için verileri işle
      for (const [tcNo, rows] of rowsByTcNo) {
        console.log(`\n>>> ${tcNo} için ${rows.length} satır işlenecek <<<`);

        // İlk satırdan hükümlü verisini al
        const firstRow = rows[0];
        const validatedConvict = validateConvictData(firstRow.data, firstRow.rowNumber);
        if (validatedConvict.isValid) {
          validConvictCount++;
          parsedConvicts.push(validatedConvict);
        }

        // Tüm satırlardan imza periyotlarını ve muafiyetleri al
        rows.forEach(({ data, rowNumber }) => {
          console.log(`\n--- ${tcNo} için ${rowNumber}. satır işleniyor ---`);
          console.log('Ham Veriler:', {
            imzaBaşlangıç: data['İmza Periyodu Başlangıç'],
            imzaBitiş: data['İmza Periyodu Bitiş'],
            imzaSıklıkTürü: data['İmza Sıklığı Türü'],
            imzaSıklıkDeğeri: data['İmza Sıklığı Değeri'],
            muafiyetTürü: data['Muafiyet Türü'],
            muafiyetBaşlangıç: data['Muafiyet Başlangıç'],
            muafiyetBitiş: data['Muafiyet Bitiş']
          });

          // İmza periyodu kontrolü
          const validatedSignaturePeriod = validateSignaturePeriodData(data, rowNumber);
          if (validatedSignaturePeriod) {
            console.log(`${tcNo} - ${rowNumber}. satır imza periyodu doğrulama:`, {
              geçerli: validatedSignaturePeriod.isValid,
              başlangıç: validatedSignaturePeriod.start_date,
              bitiş: validatedSignaturePeriod.end_date,
              sıklıkTürü: validatedSignaturePeriod.frequency_type,
              sıklıkDeğeri: validatedSignaturePeriod.frequency_value,
              hatalar: validatedSignaturePeriod.errors.length > 0 ? validatedSignaturePeriod.errors : 'Yok'
            });

            if (validatedSignaturePeriod.isValid) {
              if (validatedSignaturePeriod.frequency_type === 'WEEKLY' && validatedSignaturePeriod.frequency_value.includes(',')) {
                const individualDays = validatedSignaturePeriod.frequency_value.split(',').map(d => d.trim().toUpperCase());
                individualDays.forEach(day => {
                  if (day) { // Boş günleri atla (doğrulama zaten yakalamış olmalı ama garanti olsun)
                    const singleDayPeriod: ParsedSignaturePeriod = {
                      ...validatedSignaturePeriod,
                      frequency_value: day, // Gün değerini tek bir gün ile değiştir
                    };
                    parsedSignaturePeriods.push(singleDayPeriod);
                    validPeriodCount++; 
                    console.log(`✓ ${tcNo} - ${rowNumber}. satırdan (gün: ${day}): İmza periyodu eklendi (Toplam: ${parsedSignaturePeriods.length})`);
                  }
                });
              } else if (validatedSignaturePeriod.frequency_type === 'MONTHLY_SPECIFIC' && validatedSignaturePeriod.frequency_value.includes(',')) {
                const individualDays = validatedSignaturePeriod.frequency_value.split(',').map(d => d.trim());
                individualDays.forEach(day => {
                  if (day) { // Boş günleri atla
                    const singleDayPeriod: ParsedSignaturePeriod = {
                      ...validatedSignaturePeriod,
                      frequency_value: day, // Gün değerini tek bir gün ile değiştir
                    };
                    parsedSignaturePeriods.push(singleDayPeriod);
                    validPeriodCount++; 
                    console.log(`✓ ${tcNo} - ${rowNumber}. satırdan (ayın ${day}. günü): İmza periyodu eklendi (Toplam: ${parsedSignaturePeriods.length})`);
                  }
                });
              } else {
                // Tek günlük WEEKLY, tek günlük MONTHLY_SPECIFIC veya diğer sıklık türleri (X_DAYS)
                parsedSignaturePeriods.push(validatedSignaturePeriod);
                validPeriodCount++;
                console.log(`✓ ${tcNo} - ${rowNumber}. satır: İmza periyodu eklendi (Toplam: ${parsedSignaturePeriods.length})`);
              }
            } else {
              console.log(`✗ ${tcNo} - ${rowNumber}. satır: İmza periyodu geçersiz. Hatalar:`, validatedSignaturePeriod.errors);
            }
          } else {
            console.log(`- ${tcNo} - ${rowNumber}. satır: İmza periyodu verisi yok`);
          }

          // Muafiyet kontrolü
          const validatedExemption = validateExemptionData(data, rowNumber);
          if (validatedExemption) {
            console.log(`${tcNo} - ${rowNumber}. satır muafiyet doğrulama:`, {
              geçerli: validatedExemption.isValid,
              tür: validatedExemption.exemption_type,
              başlangıç: validatedExemption.start_date,
              bitiş: validatedExemption.end_date,
              hatalar: validatedExemption.errors.length > 0 ? validatedExemption.errors : 'Yok'
            });

            if (validatedExemption.isValid) {
              validExemptionCount++;
              parsedExemptions.push(validatedExemption);
              console.log(`✓ ${tcNo} - ${rowNumber}. satır: Muafiyet eklendi (Toplam: ${parsedExemptions.length})`);
            } else {
              console.log(`✗ ${tcNo} - ${rowNumber}. satır: Muafiyet geçersiz. Hatalar:`, validatedExemption.errors);
            }
          } else {
            console.log(`- ${tcNo} - ${rowNumber}. satır: Muafiyet verisi yok`);
          }
        });
      }

      console.log('\n=== Veri İşleme Özeti ===');
      console.log('Toplam Satır:', totalRows);
      console.log('Geçerli Hükümlü:', validConvictCount);
      console.log('Geçerli İmza Periyodu:', validPeriodCount);
      console.log('Geçerli Muafiyet:', validExemptionCount);
      
      console.log('\nHükümlüler:', parsedConvicts.map(c => ({
        tcNo: c.tc_no,
        ad: c.first_name,
        soyad: c.last_name,
        geçerli: c.isValid,
        hatalar: c.errors
      })));
      
      console.log('\nİmza Periyotları:', parsedSignaturePeriods.map(p => ({
        tcNo: p.convict_tc_no,
        başlangıç: p.start_date,
        bitiş: p.end_date,
        sıklıkTürü: p.frequency_type,
        sıklıkDeğeri: p.frequency_value
      })));
      
      console.log('\nMuafiyetler:', parsedExemptions.map(e => ({
        tcNo: e.convict_tc_no,
        tür: e.exemption_type,
        başlangıç: e.start_date,
        bitiş: e.end_date
      })));

      setParsedData({
        convicts: parsedConvicts,
        signaturePeriods: parsedSignaturePeriods,
        exemptions: parsedExemptions
      });

    } catch (error) {
      toast({
        title: 'Dosya Okuma Hatası',
        description: error instanceof Error ? error.message : 'Dosya okunamadı',
        variant: 'destructive',
      });
      resetImportState();
    } finally {
      setIsParsingFile(false);
    }
  }, [toast]);

  const handleImport = () => {
    const validConvicts = parsedData.convicts.filter(convict => convict.isValid);
    const validSignaturePeriods = parsedData.signaturePeriods.filter(period => period.isValid);
    const validExemptions = parsedData.exemptions.filter(exemption => exemption.isValid);
    
    console.log('İçe Aktarma Başlangıcı:', {
      hükümlüSayısı: validConvicts.length,
      periyotSayısı: validSignaturePeriods.length,
      muafiyetSayısı: validExemptions.length,
      periyotlar: validSignaturePeriods,
      muafiyetler: validExemptions
    });
    
    if (validConvicts.length === 0) {
      toast({
        title: 'İçe Aktarma Hatası',
        description: 'İçe aktarılacak geçerli hükümlü verisi bulunamadı.',
        variant: 'destructive',
      });
      return;
    }

    const convictsToImport = validConvicts.map(({ rowNumber, isValid, errors, ...convict }) => convict);
    const signaturePeriodsToImport = validSignaturePeriods.map(({ rowNumber, isValid, errors, ...period }) => period);
    const exemptionsToImport = validExemptions.map(({ rowNumber, isValid, errors, ...exemption }) => exemption);

    bulkImportMutation.mutate({
      convicts: convictsToImport,
      signaturePeriods: signaturePeriodsToImport,
      exemptions: exemptionsToImport
    });
  };

  const downloadTemplate = async () => {
    const templateData = [
      [
        // Hükümlü bilgileri sütunları - Export ile tam eşleşen sıralama
        'TC Kimlik No', 'Ad', 'Soyad', 'Hükümlü Telefon Numarası', 'Yakınına Ait Telefon Numarası', 'Adres Bilgileri', 'Dosya Numarası', 'Denetim Başlangıç Tarihi', 'Denetim Bitiş Tarihi', 'Aktif', 'Notlar',
        // İmza periyodu sütunları
        'İmza Periyodu Başlangıç', 'İmza Periyodu Bitiş', 'İmza Sıklığı Türü', 'İmza Sıklığı Değeri', 'İmza Referans Tarihi', 'İmza Periyodu Aktif',
        // Zaman kısıtlamaları sütunları
        'İmza Başlangıç Saati', 'İmza Bitiş Saati', 'İzin Verilen Günler',
        // Muafiyet sütunları
        'Muafiyet Türü', 'Muafiyet Başlangıç', 'Muafiyet Bitiş', 'Muafiyet Açıklaması', 'Muafiyet Belgesi', 'Muafiyet Aktif'
      ],
      [
        // Örnek 1: Tam kapsamlı hükümlü verisi (haftalık imza + zaman kısıtlaması + muafiyet)
        '12345678901', 'Ahmet', 'Yılmaz', '+90 532 123 45 67', '+90 505 987 65 43', 'Atatürk Mahallesi, 123. Sokak No:15 Daire:3, Çankaya/Ankara', '2025/1 NKL', '2025-01-01', '2025-12-31', 'Evet', 'Denetim altındaki hükümlü - tam bilgilerle',
        // Haftalık imza periyodu
        '2025-01-01', '2025-12-31', 'HAFTALIK', 'PAZARTESİ,ÇARŞAMBA,CUMA', '2025-01-01', 'Evet',
        // Zaman kısıtlamaları
        '09:00', '18:00', 'pazartesi,çarşamba,cuma',
        // Yıllık izin muafiyeti
        'İZİN', '2025-07-15', '2025-07-29', 'Yıllık izin muafiyeti', '', 'Evet'
      ],
      [
        // Örnek 2: Aynı hükümlü için farklı imza periyodu
        '12345678901', 'Ahmet', 'Yılmaz', '+90 532 123 45 67', '+90 505 987 65 43', 'Atatürk Mahallesi, 123. Sokak No:15 Daire:3, Çankaya/Ankara', '2025/1 NKL', '2025-01-01', '2025-12-31', 'Evet', 'Denetim altındaki hükümlü - tam bilgilerle',
        // Perşembe günleri imza
        '2025-01-01', '2025-06-30', 'HAFTALIK', 'PERŞEMBE', '2025-01-02', 'Evet',
        // Zaman kısıtlaması yok
        '', '', '',
        // Sağlık raporu muafiyeti
        'SAĞLIK RAPORU', '2025-05-10', '2025-05-20', 'Sağlık kontrolü muafiyeti', 'saglik_raporu_12345.pdf', 'Evet'
      ],
      [
        // Örnek 3: Farklı hükümlü - günlük imza sistemi
        '98765432109', 'Fatma', 'Demir', '+90 541 888 99 00', '+90 216 333 44 55', 'Bağdat Caddesi, Sevinç Apartmanı No:78 Kat:5 Daire:12, Kadıköy/İstanbul', '2025/2 NKL', '2025-02-01', '2026-01-31', 'Evet', 'Uzun süreli denetim - günlük takip',
        // Her 3 günde bir imza
        '2025-02-01', '2026-01-31', 'GÜNLÜK', '3', '2025-02-01', 'Evet',
        // Mesai saatleri kısıtlaması
        '08:30', '17:30', 'pazartesi,salı,çarşamba,perşembe,cuma',
        // Muafiyet yok
        '', '', '', '', '', ''
      ],
      [
        // Örnek 4: Aylık imza sistemi
        '11122334455', 'Mehmet', 'Kaya', '', '+90 536 555 44 33', 'İstiklal Caddesi No:42 Daire:8, Beyoğlu/İstanbul', '2025/34 NKL', '2025-03-01', '2025-09-30', 'Evet', 'Kısa süreli denetim - aylık takip',
        // Ayın 15'inde imza
        '2025-03-01', '2025-09-30', 'AYLIK', '15', '2025-03-15', 'Evet',
        // Hafta sonu kısıtlaması
        '10:00', '16:00', 'cumartesi,pazar',
        // Muafiyet yok
        '', '', '', '', '', ''
      ],
      [
        // Örnek 5: Minimal veri - sadece zorunlu alanlar
        '55566677788', 'Ali', 'Özkan', '', '', '', '', '2025-04-01', '2025-10-01', 'Evet', 'Minimal verili hükümlü',
        // İmza periyodu yok
        '', '', '', '', '', '',
        // Zaman kısıtlaması yok
        '', '', '',
        // Muafiyet yok
        '', '', '', '', '', ''
      ]
    ];

    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(templateData);

    // Create instructions worksheet
    const instructionsData = [
      ['EXCEL ŞABLONU KULLANIM KILAVUZU - HÜKÜMLÜ VERİ YÖNETİMİ'],
      [''],
      ['Bu şablon hükümlü kayıtları, imza periyotları ve muafiyet bilgilerini sisteme toplu olarak aktarmak için kullanılır.'],
      ['Export edilen veriler de aynı formatı kullanır, böylece export-import uyumluluğu sağlanır.'],
      [''],
      ['=== ZORUNLU SÜTUNLAR ==='],
      ['Bu sütunlar mutlaka doldurulmalıdır:'],
      ['• TC Kimlik No: 11 haneli geçerli TC kimlik numarası (örn: 12345678901)'],
      ['• Ad: Hükümlünün adı (örn: Ahmet)'],
      ['• Soyad: Hükümlünün soyadı (örn: Yılmaz)'],
      ['• Denetim Başlangıç Tarihi: YYYY-MM-DD formatında (örn: 2025-01-01)'],
      ['• Denetim Bitiş Tarihi: YYYY-MM-DD formatında (örn: 2025-12-31)'],
      ['• Aktif: "Evet", "Hayır", "Yes", "No" veya boş (boş = Evet)'],
      [''],
      ['=== İSTEĞE BAĞLI SÜTUNLAR ==='],
      [''],
      ['--- İLETİŞİM BİLGİLERİ ---'],
      ['• Hükümlü Telefon Numarası: Hükümlünün telefon numarası (örn: +90 532 123 45 67)'],
      ['• Yakınına Ait Telefon Numarası: Yakınının telefon numarası (örn: +90 505 987 65 43)'],
      ['• Adres Bilgileri: Tam adres bilgisi (örn: Atatürk Mahallesi, 123. Sokak No:15 Daire:3, Çankaya/Ankara)'],
      ['• Dosya Numarası: Hükümlünün dosya numarası (örn: 2025/1 NKL)'],
      ['• Notlar: Ek açıklamalar ve özel durumlar'],
      [''],
      ['--- İMZA PERİYODU BİLGİLERİ ---'],
      ['Hükümlünün imza vermesi gereken zamanları tanımlar:'],
      ['• İmza Periyodu Başlangıç: YYYY-MM-DD formatında (örn: 2025-01-01)'],
      ['• İmza Periyodu Bitiş: YYYY-MM-DD formatında (örn: 2025-12-31)'],
      ['• İmza Sıklığı Türü: HAFTALIK, GÜNLÜK veya AYLIK'],
      ['• İmza Sıklığı Değeri:'],
      ['  - HAFTALIK için: PAZARTESİ, SALI, ÇARŞAMBA, PERŞEMBE, CUMA, CUMARTESİ, PAZAR'],
      ['    * Birden fazla gün için: PAZARTESİ,ÇARŞAMBA,CUMA (virgülle ayırın)'],
      ['  - GÜNLÜK için: 1-30 arası sayı (kaç günde bir imza vereceği)'],
      ['  - AYLIK için: 1-31 arası gün numarası (ayın hangi günü)'],
      ['• İmza Referans Tarihi: YYYY-MM-DD formatında (isteğe bağlı, periyot hesaplamaları için)'],
      ['• İmza Periyodu Aktif: "Evet", "Hayır" veya boş (boş = Evet)'],
      [''],
      ['--- ZAMAN KISITLAMALARI ---'],
      ['İmza verme saatlerini ve günlerini kısıtlar:'],
      ['• İmza Başlangıç Saati: HH:MM formatında (örn: 09:00)'],
      ['• İmza Bitiş Saati: HH:MM formatında (örn: 18:00)'],
      ['• İzin Verilen Günler: Virgülle ayrılmış gün isimleri'],
      ['  - Türkçe: pazartesi,çarşamba,cuma'],
      ['  - İngilizce: monday,wednesday,friday'],
      ['  - Hem başlangıç hem bitiş saati belirtilmeli veya ikisi de boş bırakılmalı'],
      [''],
      ['--- MUAFİYET BİLGİLERİ ---'],
      ['Hükümlünün imza yükümlülüğünden muaf olduğu dönemler:'],
      ['• Muafiyet Türü: İZİN veya SAĞLIK RAPORU'],
      ['• Muafiyet Başlangıç: YYYY-MM-DD formatında (örn: 2025-07-15)'],
      ['• Muafiyet Bitiş: YYYY-MM-DD formatında (örn: 2025-07-29)'],
      ['• Muafiyet Açıklaması: Serbest metin (örn: Yıllık izin muafiyeti)'],
      ['• Muafiyet Belgesi: Dosya yolu (isteğe bağlı)'],
      ['• Muafiyet Aktif: "Evet", "Hayır" veya boş (boş = Evet)'],
      [''],
      ['=== KULLANIM KURALLARI ==='],
      [''],
      ['1. TEMEL KURALLAR:'],
      ['   • Her satır için TC Kimlik No zorunludur'],
      ['   • Mevcut TC kimlik numaraları güncellenir, yeni olanlar eklenir'],
      ['   • Tarih formatı mutlaka YYYY-MM-DD olmalıdır'],
      ['   • Boş bırakılan sütunlar göz ardı edilir'],
      [''],
      ['2. ÇOKLU İMZA PERİYODU:'],
      ['   • Aynı hükümlü için birden fazla imza periyodu eklemek istiyorsanız,'],
      ['     hükümlü bilgilerini tekrar eden yeni satırlar ekleyin'],
      ['   • Her satır için sadece bir imza periyodu eklenebilir'],
      ['   • Farklı imza periyotları farklı satırlarda tanımlanmalıdır'],
      [''],
      ['3. ÖRNEK SENARYOLAR:'],
      ['   • Haftada 3 gün imza: İmza Sıklığı Türü=HAFTALIK, Değeri=PAZARTESİ,ÇARŞAMBA,CUMA'],
      ['   • Her 3 günde bir imza: İmza Sıklığı Türü=GÜNLÜK, Değeri=3'],
      ['   • Ayın 15\'inde imza: İmza Sıklığı Türü=AYLIK, Değeri=15'],
      ['   • Mesai saatleri kısıtlaması: Başlangıç=09:00, Bitiş=18:00'],
      ['   • Hafta içi kısıtlaması: İzin Verilen Günler=pazartesi,salı,çarşamba,perşembe,cuma'],
      [''],
      ['4. HATA AYIKLAMA:'],
      ['   • Import sırasında hatalı veriler raporlanacaktır'],
      ['   • Sadece geçerli veriler sisteme aktarılır'],
      ['   • Hataları düzeltip dosyayı tekrar yükleyebilirsiniz'],
      [''],
      ['=== EXPORT UYUMLULUĞU ==='],
      ['Bu şablon sistem export özelliği ile tam uyumludur.'],
      ['Export edilen dosyaları düzenleyip tekrar import edebilirsiniz.'],
      ['Sütun sıralaması ve isimleri export formatıyla birebir aynıdır.']
    ];

    const instructionsWorksheet = XLSX.utils.aoa_to_sheet(instructionsData);
    
    // Set column width for instructions
    instructionsWorksheet['!cols'] = [{ wch: 80 }];

    // Set column widths for main worksheet - Export ile uyumlu genişlikler
    const colWidths = [
      // Hükümlü temel bilgileri
      { wch: 15 }, // TC Kimlik No
      { wch: 12 }, // Ad
      { wch: 12 }, // Soyad
      { wch: 20 }, // Hükümlü Telefon Numarası
      { wch: 22 }, // Yakınına Ait Telefon Numarası
      { wch: 40 }, // Adres Bilgileri
      { wch: 16 }, // Dosya Numarası
      { wch: 22 }, // Denetim Başlangıç Tarihi
      { wch: 22 }, // Denetim Bitiş Tarihi
      { wch: 8 },  // Aktif
      { wch: 30 }, // Notlar
      // İmza periyodu bilgileri
      { wch: 22 }, // İmza Periyodu Başlangıç
      { wch: 22 }, // İmza Periyodu Bitiş
      { wch: 18 }, // İmza Sıklığı Türü
      { wch: 25 }, // İmza Sıklığı Değeri
      { wch: 20 }, // İmza Referans Tarihi
      { wch: 18 }, // İmza Periyodu Aktif
      // Zaman kısıtlamaları
      { wch: 18 }, // İmza Başlangıç Saati
      { wch: 18 }, // İmza Bitiş Saati
      { wch: 30 }, // İzin Verilen Günler
      // Muafiyet bilgileri
      { wch: 16 }, // Muafiyet Türü
      { wch: 20 }, // Muafiyet Başlangıç
      { wch: 20 }, // Muafiyet Bitiş
      { wch: 30 }, // Muafiyet Açıklaması
      { wch: 25 }, // Muafiyet Belgesi
      { wch: 16 }, // Muafiyet Aktif
    ];
    worksheet['!cols'] = colWidths;

    // Add worksheets to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Hükümlü Verileri');
    XLSX.utils.book_append_sheet(workbook, instructionsWorksheet, 'Kullanım Kılavuzu');
    
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    
    try {
      // Show save dialog
      const filePath = await save({
        defaultPath: 'hukumlu-sablonu.xlsx',
        filters: [
          { name: 'Excel Dosyaları', extensions: ['xlsx'] },
          { name: 'Tüm Dosyalar', extensions: ['*'] }
        ]
      });
      
      if (!filePath) {
        // User canceled
        return;
      }
      
      // Save the binary file
      await saveBinaryFile(filePath, new Uint8Array(excelBuffer));
      
      toast({
        title: 'Şablon İndirildi',
        description: 'Excel şablonu başarıyla kaydedildi.',
      });
    } catch (error) {
      toast({
        title: 'Şablon İndirme Hatası',
        description: 'Excel şablonu kaydedilirken bir hata oluştu.',
        variant: 'destructive',
      });
    }
  };

  const validConvictCount = parsedData.convicts.filter(item => item.isValid).length;
  const invalidConvictCount = parsedData.convicts.length - validConvictCount;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" onClick={() => setIsOpen(true)}>
          <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
          Excel'den İçe Aktar
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Hükümlü Listesi - Excel İçe Aktarma</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Excel Şablonu</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">
                Hükümlü verilerini, imza periyotlarını ve muafiyet bilgilerini sisteme toplu olarak aktarmak için Excel şablonunu indirin. 
                Şablon, sistem export özelliği ile tam uyumludur - export edilen dosyaları düzenleyip tekrar import edebilirsiniz.
                <br /><br />
                <strong>Şablonda 5 adet örnek kayıt bulunur:</strong>
                <br />• Tam kapsamlı hükümlü (çoklu haftalık imza + zaman kısıtlaması + muafiyet)
                <br />• Aynı hükümlü için farklı imza periyodu + farklı muafiyet
                <br />• Günlük imza sistemi örneği
                <br />• Aylık imza sistemi örneği  
                <br />• Minimal veri örneği (sadece zorunlu alanlar)
                <br /><br />
                Mevcut TC kimlik numaraları güncellenir, yeni olanlar eklenir. 
                Sistem Türkçe değerler (HAFTALIK, PAZARTESİ, İZİN, SAĞLIK RAPORU) kabul eder. 
                Detaylı kullanım kılavuzu şablonun "Kullanım Kılavuzu" sayfasındadır.
              </p>
              <Button variant="outline" onClick={downloadTemplate}>
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Şablonu İndir
              </Button>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Excel Dosyası Seçin</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Label htmlFor="excel-file">Excel Dosyası (.xlsx)</Label>
                <Input
                  id="excel-file"
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileSelect}
                  disabled={isParsingFile || bulkImportMutation.isPending}
                />
                {selectedFile && (
                  <p className="text-sm text-gray-600">
                    Seçilen dosya: {selectedFile.name}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Parse Progress */}
          {isParsingFile && (
            <Alert>
              <AlertDescription>
                Excel dosyası okunuyor ve veriler doğrulanıyor...
              </AlertDescription>
            </Alert>
          )}

          {/* Import Progress */}
          {bulkImportMutation.isPending && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>İçe aktarma devam ediyor...</span>
                    <span>{importProgress}%</span>
                  </div>
                  <Progress value={importProgress} />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Data Preview and Validation Results */}
          {parsedData.convicts.length > 0 && !isParsingFile && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Veri Önizleme ve Doğrulama</CardTitle>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircleIcon className="h-3 w-3 mr-1" />
                    Geçerli Hükümlü: {validConvictCount}
                  </Badge>
                  {invalidConvictCount > 0 && (
                    <Badge variant="destructive">
                      <XCircleIcon className="h-3 w-3 mr-1" />
                      Hatalı Hükümlü: {invalidConvictCount}
                    </Badge>
                  )}
                  {parsedData.signaturePeriods.length > 0 && (
                    <Badge variant="secondary">
                      İmza Periyodu: {parsedData.signaturePeriods.length}
                    </Badge>
                  )}
                  {parsedData.exemptions.length > 0 && (
                    <Badge variant="secondary">
                      Muafiyet: {parsedData.exemptions.length}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="max-h-64 overflow-y-auto">
                  <table className="w-full text-xs">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Satır</th>
                        <th className="text-left p-2">TC No</th>
                        <th className="text-left p-2">Ad Soyad</th>
                        <th className="text-left p-2">Denetim Dönemi</th>
                        <th className="text-left p-2">Durum</th>
                        <th className="text-left p-2">Hatalar</th>
                      </tr>
                    </thead>
                    <tbody>
                      {parsedData.convicts.map((item, index) => (
                        <tr key={index} className={`border-b ${item.isValid ? 'bg-green-50' : 'bg-red-50'}`}>
                          <td className="p-2">{item.rowNumber}</td>
                          <td className="p-2">{item.tc_no}</td>
                          <td className="p-2">{item.first_name} {item.last_name}</td>
                          <td className="p-2">
                            {item.supervision_start_date} - {item.supervision_end_date}
                          </td>
                          <td className="p-2">
                            {item.isValid ? (
                              <Badge variant="default" className="bg-green-100 text-green-800">
                                Geçerli
                              </Badge>
                            ) : (
                              <Badge variant="destructive">
                                Hatalı
                              </Badge>
                            )}
                          </td>
                          <td className="p-2">
                            {item.errors.length > 0 && (
                              <div className="space-y-1">
                                {item.errors.map((error, errorIndex) => (
                                  <div key={errorIndex} className="text-xs text-red-600">
                                    {error.field}: {error.message}
                                  </div>
                                ))}
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {invalidConvictCount > 0 && (
                  <Alert className="mt-4">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                    <AlertDescription>
                      {invalidConvictCount} satırda hata bulundu. Sadece geçerli veriler içe aktarılacaktır.
                      Hataları düzeltmek için Excel dosyasını güncelleyip tekrar yükleyebilirsiniz.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          {parsedData.convicts.length > 0 && !isParsingFile && (
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={resetImportState}>
                İptal
              </Button>
              <Button 
                onClick={handleImport}
                disabled={validConvictCount === 0 || bulkImportMutation.isPending}
              >
                {(() => {
                  const validSignaturePeriods = parsedData.signaturePeriods.filter(p => p.isValid).length;
                  const validExemptions = parsedData.exemptions.filter(e => e.isValid).length;
                  let text = `${validConvictCount} Hükümlü`;
                  if (validSignaturePeriods > 0) text += `, ${validSignaturePeriods} İmza Periyodu`;
                  if (validExemptions > 0) text += `, ${validExemptions} Muafiyet`;
                  return `${text} İçe Aktar`;
                })()}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
