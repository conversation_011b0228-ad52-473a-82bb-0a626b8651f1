use crate::database::{
    create_convict, create_signature, create_signature_period, create_user, 
    find_user_by_username, get_all_users, get_dashboard_stats, 
    get_signature_periods_by_convict, get_signatures_by_convict, 
    get_convicts as db_get_convicts, search_convict_by_tc as db_search_convict_by_tc, 
    search_convicts_by_name as db_search_convicts_by_name, search_convicts_by_file_number as db_search_convicts_by_file_number, validate_database_schema,
    update_convict, update_signature_period, delete_convict, delete_signature_period, delete_signature,
    get_convict_by_id as db_get_convict_by_id, with_transaction,
    check_signature_exists, get_all_expected_signatures_for_today_db, get_violations_for_last_30_days_db,
    create_exemption, get_exemptions_by_convict, get_active_exemptions_by_convict_and_date, update_exemption, delete_exemption,
    get_convict_full_details,

    Convict, DashboardStats, DatabaseConnection, InsertConvict, InsertSignature, 
    InsertSignaturePeriod, InsertUser, Signature, SignaturePeriod, User, CompletedSignature, ExpectedSignature,
    ViolationRecord, Exemption, InsertExemption, ConvictFullDetails,
};
use std::fs;
use tauri::{State, AppHandle, Emitter};
use chrono::{NaiveDate, Datelike, Weekday};
use serde::Serialize;

// Event types for real-time updates
#[derive(Clone, serde::Serialize)]
pub struct DataChangeEvent {
    pub event_type: String,
    pub table: String,
    pub id: Option<i32>,
    pub timestamp: i64,
}

// Helper function to emit data change events
fn emit_data_change(app: &AppHandle, event_type: &str, table: &str, id: Option<i32>) {
    let event = DataChangeEvent {
        event_type: event_type.to_string(),
        table: table.to_string(),
        id,
        timestamp: chrono::Utc::now().timestamp(),
    };
    
    // Try Tauri v2 emit method
    if let Err(e) = app.emit("data-change", &event) {
        eprintln!("Failed to emit data change event: {}", e);
    }
}

#[tauri::command]
pub async fn get_users(db: State<'_, DatabaseConnection>) -> Result<Vec<User>, String> {
    get_all_users(&db).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_new_user(
    db: State<'_, DatabaseConnection>,
    user: InsertUser,
) -> Result<User, String> {
    create_user(&db, user).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_convicts(db: State<'_, DatabaseConnection>) -> Result<Vec<Convict>, String> {
    db_get_convicts(&db)
        .map_err(|e| format!("Failed to get convicts: {}", e))
}

#[tauri::command]
pub async fn create_new_convict(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    convict: InsertConvict,
) -> Result<Convict, String> {
    match create_convict(&db, convict) {
        Ok(new_convict) => {
            // Emit real-time event for convict creation
            emit_data_change(&app, "created", "convicts", Some(new_convict.id));
            Ok(new_convict)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn bulk_create_convicts(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    convicts: Vec<InsertConvict>,
) -> Result<Vec<Convict>, String> {
    let mut created_convicts = Vec::new();
    let mut errors = Vec::new();
    
    for (index, convict) in convicts.iter().enumerate() {
        match create_convict(&db, convict.clone()) {
            Ok(new_convict) => {
                let convict_id = new_convict.id;
                created_convicts.push(new_convict);
                // Emit real-time event for each convict creation
                emit_data_change(&app, "created", "convicts", Some(convict_id));
            }
            Err(e) => {
                let error_message = if e.to_string().contains("UNIQUE constraint failed: convicts.tc_no") {
                    format!("TC Kimlik No {} zaten sistemde kayıtlı", convict.tc_no)
                } else {
                    e.to_string()
                };
                errors.push(format!("Row {}: {}", index + 1, error_message));
            }
        }
    }
    
    if !errors.is_empty() {
        return Err(format!("Some convicts failed to create: {}", errors.join("; ")));
    }
    
    Ok(created_convicts)
}

#[tauri::command]
pub async fn bulk_upsert_convicts(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    convicts: Vec<InsertConvict>,
) -> Result<Vec<Convict>, String> {
    let mut result_convicts = Vec::new();
    let mut created_count = 0;
    let mut updated_count = 0;
    let mut errors = Vec::new();
    
    for (index, convict) in convicts.iter().enumerate() {
        match crate::database::upsert_convict(&db, convict.clone()) {
            Ok((upserted_convict, was_created)) => {
                let convict_id = upserted_convict.id;
                result_convicts.push(upserted_convict);
                
                if was_created {
                    created_count += 1;
                    emit_data_change(&app, "created", "convicts", Some(convict_id));
                } else {
                    updated_count += 1;
                    emit_data_change(&app, "updated", "convicts", Some(convict_id));
                }
            }
            Err(e) => {
                let error_message = if e.to_string().contains("UNIQUE constraint failed") {
                    format!("TC Kimlik No {} zaten sistemde kayıtlı", convict.tc_no)
                } else {
                    e.to_string()
                };
                errors.push(format!("Row {}: {}", index + 1, error_message));
            }
        }
    }
    
    if !errors.is_empty() {
        return Err(format!("Some convicts failed to process: {}", errors.join("; ")));
    }
    
    println!("Bulk upsert completed: {} created, {} updated", created_count, updated_count);
    Ok(result_convicts)
}

#[tauri::command]
pub async fn get_dashboard_statistics(
    db: State<'_, DatabaseConnection>,
) -> Result<DashboardStats, String> {
    get_dashboard_stats(&db).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_new_signature(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    signature: InsertSignature,
) -> Result<Signature, String> {
    match create_signature(&db, signature) {
        Ok(new_signature) => {
            // Emit real-time event for signature creation
            emit_data_change(&app, "created", "signatures", Some(new_signature.id));
            Ok(new_signature)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn get_convict_signatures(
    db: State<'_, DatabaseConnection>,
    convict_id: i32,
) -> Result<Vec<Signature>, String> {
    get_signatures_by_convict(&db, convict_id).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn authenticate_user(
    db: State<'_, DatabaseConnection>,
    username: String,
    password: String,
) -> Result<Option<User>, String> {
    // Find user by username
    let user = find_user_by_username(&db, &username)
        .map_err(|e| e.to_string())?;
    
    match user {
        Some(user) if user.is_active => {
            // Verify password using bcrypt
            match bcrypt::verify(&password, &user.password) {
                Ok(true) => Ok(Some(user)),
                Ok(false) => Ok(None), // Invalid password
                Err(e) => Err(format!("Password verification error: {}", e)),
            }
        }
        _ => Ok(None), // User not found or inactive
    }
}

#[tauri::command]
pub async fn search_convict_by_tc(
    db: State<'_, DatabaseConnection>,
    tc_no: String,
) -> Result<Option<Convict>, String> {
    db_search_convict_by_tc(&db, &tc_no)
        .map_err(|e| format!("Failed to search convict by TC: {}", e))
}

#[tauri::command]
pub async fn search_convicts_by_name(
    db: State<'_, DatabaseConnection>,
    first_name: String,
    last_name: String,
) -> Result<Vec<Convict>, String> {
    db_search_convicts_by_name(&db, &first_name, &last_name)
        .map_err(|e| format!("Failed to search convicts by name: {}", e))
}

#[tauri::command]
pub async fn search_convicts_by_file_number(
    db: State<'_, DatabaseConnection>,
    file_number: String,
) -> Result<Vec<Convict>, String> {
    db_search_convicts_by_file_number(&db, &file_number)
        .map_err(|e| format!("Failed to search convicts by file number: {}", e))
}

#[tauri::command]
pub async fn create_new_signature_period(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    period: InsertSignaturePeriod,
) -> Result<SignaturePeriod, String> {
    match create_signature_period(&db, period) {
        Ok(new_period) => {
            // Emit real-time event for signature period creation
            emit_data_change(&app, "created", "signature_periods", Some(new_period.id));
            Ok(new_period)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn get_signature_periods(
    db: State<'_, DatabaseConnection>,
    convict_id: i32,
) -> Result<Vec<SignaturePeriod>, String> {
    get_signature_periods_by_convict(&db, convict_id).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn find_user_by_username_cmd(
    db: State<'_, DatabaseConnection>,
    username: String,
) -> Result<Option<User>, String> {
    find_user_by_username(&db, &username).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn validate_database(
    db: State<'_, DatabaseConnection>,
) -> Result<bool, String> {
    validate_database_schema(&db).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_database_info() -> Result<String, String> {
    use crate::database::get_database_path;
    let db_path = get_database_path();
    Ok(format!("Database path: {:?}", db_path))
}

// Update operations with real-time events
#[tauri::command]
pub async fn update_convict_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
    convict: InsertConvict,
) -> Result<Convict, String> {
    match update_convict(&db, id, convict) {
        Ok(updated_convict) => {
            // Emit real-time event for convict update
            emit_data_change(&app, "updated", "convicts", Some(updated_convict.id));
            Ok(updated_convict)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn update_signature_period_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
    period: InsertSignaturePeriod,
) -> Result<SignaturePeriod, String> {
    match update_signature_period(&db, id, period) {
        Ok(updated_period) => {
            // Emit real-time event for signature period update
            emit_data_change(&app, "updated", "signature_periods", Some(updated_period.id));
            Ok(updated_period)
        }
        Err(e) => Err(e.to_string())
    }
}

// Delete operations with real-time events
#[tauri::command]
pub async fn delete_convict_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
) -> Result<bool, String> {
    match delete_convict(&db, id) {
        Ok(success) => {
            if success {
                // Emit real-time event for convict deletion
                emit_data_change(&app, "deleted", "convicts", Some(id));
            }
            Ok(success)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn bulk_delete_convicts_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    ids: Vec<i32>,
) -> Result<i32, String> {
    println!("bulk_delete_convicts_data called with {} IDs: {:?}", ids.len(), ids);
    
    if ids.is_empty() {
        return Err("No convict IDs provided for deletion".to_string());
    }
    
    match crate::database::bulk_delete_convicts(&db, ids.clone()) {
        Ok(deleted_count) => {
            println!("Successfully deleted {} convicts", deleted_count);
            if deleted_count > 0 {
                // Emit real-time event for bulk deletion
                emit_data_change(&app, "bulk_deleted", "convicts", None);
            }
            Ok(deleted_count)
        }
        Err(e) => {
            eprintln!("Error in bulk_delete_convicts_data: {}", e);
            Err(format!("Bulk delete failed: {}", e))
        }
    }
}

#[tauri::command]
pub async fn delete_signature_period_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
) -> Result<bool, String> {
    match delete_signature_period(&db, id) {
        Ok(success) => {
            if success {
                // Emit real-time event for signature period deletion
                emit_data_change(&app, "deleted", "signature_periods", Some(id));
            }
            Ok(success)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn delete_signature_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
) -> Result<bool, String> {
    match delete_signature(&db, id) {
        Ok(success) => {
            if success {
                // Emit real-time event for signature deletion
                emit_data_change(&app, "deleted", "signatures", Some(id));
            }
            Ok(success)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn get_convict_by_id_cmd(
    db: State<'_, DatabaseConnection>,
    id: i32,
) -> Result<Option<Convict>, String> {
    db_get_convict_by_id(&db, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn bulk_create_signatures_with_transaction(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    signatures: Vec<InsertSignature>,
) -> Result<Vec<Signature>, String> {
    // Example usage of with_transaction for complex operations
    match with_transaction(&db, |conn| {
        let mut created_signatures = Vec::new();
        
        for signature_data in signatures {
            // Execute the insert within the transaction
            conn.execute(
                "INSERT INTO signatures (convict_id, signature_date, signature_time, recorded_by) 
                 VALUES (?1, ?2, ?3, ?4)",
                rusqlite::params![
                    signature_data.convict_id,
                    signature_data.signature_date,
                    signature_data.signature_time,
                    signature_data.recorded_by
                ],
            )?;

            let signature_id = conn.last_insert_rowid() as i32;
            
            // Query the created signature
            let mut stmt = conn.prepare(
                "SELECT id, convict_id, signature_date, signature_time, recorded_by, created_at 
                 FROM signatures WHERE id = ?1"
            )?;
            
            let signature = stmt.query_row([signature_id], |row| {
                Ok(Signature {
                    id: row.get(0)?,
                    convict_id: row.get(1)?,
                    signature_date: row.get(2)?,
                    signature_time: row.get(3)?,
                    recorded_by: row.get(4)?,
                    created_at: row.get(5)?,
                })
            })?;
            
            created_signatures.push(signature);
        }
        
        Ok(created_signatures)
    }) {
        Ok(signatures) => {
            // Emit real-time event for bulk signature creation
            emit_data_change(&app, "bulk_created", "signatures", None);
            Ok(signatures)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn update_user_cmd(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
    user_data: InsertUser,
) -> Result<User, String> {
    match crate::database::update_user(&db, id, user_data) {
        Ok(updated_user) => {
            emit_data_change(&app, "updated", "users", Some(id));
            Ok(updated_user)
        }
        Err(e) => Err(format!("Failed to update user: {}", e)),
    }
}

#[tauri::command]
pub async fn delete_user_cmd(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
) -> Result<bool, String> {
    match crate::database::delete_user(&db, id) {
        Ok(success) => {
            if success {
                emit_data_change(&app, "deleted", "users", Some(id));
            }
            Ok(success)
        }
        Err(e) => Err(format!("Failed to delete user: {}", e)),
    }
}

#[derive(Debug, Serialize, Clone)]
pub struct ViolationReportItem {
    convict: Convict,
    #[serde(rename = "violationDate")]
    violation_date: String,
    #[serde(rename = "expectedFrequency")]
    expected_frequency: String,
}

fn get_frequency_text_for_period(period: &SignaturePeriod) -> String {
    match period.frequency_type.as_str() {
        "WEEKLY" => {
            let days: Vec<&str> = period.frequency_value.split(',').map(|s| s.trim()).collect();
            let day_names: Vec<String> = days.iter().map(|d| match *d {
                "MONDAY" => "Pazartesi".to_string(),
                "TUESDAY" => "Salı".to_string(),
                "WEDNESDAY" => "Çarşamba".to_string(),
                "THURSDAY" => "Perşembe".to_string(),
                "FRIDAY" => "Cuma".to_string(),
                "SATURDAY" => "Cumartesi".to_string(),
                "SUNDAY" => "Pazar".to_string(),
                _ => d.to_string(),
            }).collect();
            format!("Haftalık: {}", day_names.join(", "))
        }
        "EVERY_X_DAYS" => format!("{} günde bir", period.frequency_value),
        "MONTHLY_SPECIFIC_DAYS" => format!("Aylık: {} günleri", period.frequency_value),
        _ => "Bilinmeyen Sıklık".to_string(),
    }
}

fn is_signature_due_on_date(date_to_check: NaiveDate, period: &SignaturePeriod) -> bool {
    if !period.is_active { return false; }

    let period_start_date = NaiveDate::parse_from_str(&period.start_date, "%Y-%m-%d").unwrap_or_default();
    let period_end_date = NaiveDate::parse_from_str(&period.end_date, "%Y-%m-%d").unwrap_or_default();

    if date_to_check < period_start_date || date_to_check > period_end_date {
        return false;
    }

    match period.frequency_type.as_str() {
        "WEEKLY" => {
            let days: Vec<&str> = period.frequency_value.split(',').map(|s| s.trim()).collect();
            let weekday_to_check = date_to_check.weekday();
            days.iter().any(|day_str| match *day_str {
                "MONDAY" => weekday_to_check == Weekday::Mon,
                "TUESDAY" => weekday_to_check == Weekday::Tue,
                "WEDNESDAY" => weekday_to_check == Weekday::Wed,
                "THURSDAY" => weekday_to_check == Weekday::Thu,
                "FRIDAY" => weekday_to_check == Weekday::Fri,
                "SATURDAY" => weekday_to_check == Weekday::Sat,
                "SUNDAY" => weekday_to_check == Weekday::Sun,
                _ => false,
            })
        }
        "EVERY_X_DAYS" => {
            let x_days = period.frequency_value.parse::<u64>().unwrap_or(0);
            if x_days == 0 { return false; }
            let reference_date_str = period.reference_date.as_deref().unwrap_or(&period.start_date);
            let reference_date = NaiveDate::parse_from_str(reference_date_str, "%Y-%m-%d").unwrap_or(period_start_date);
            if date_to_check < reference_date { return false; }
            let days_diff = (date_to_check - reference_date).num_days();
            days_diff >= 0 && (days_diff as u64 % x_days == 0)
        }
        "MONTHLY_SPECIFIC_DAYS" => {
            let days_of_month: Vec<u32> = period.frequency_value.split(',')
                .filter_map(|s| s.trim().parse::<u32>().ok())
                .collect();
            days_of_month.contains(&date_to_check.day())
        }
        _ => false,
    }
}

#[tauri::command]
pub async fn get_violations_report(
    db: State<'_, DatabaseConnection>,
    start_date_str: String,
    end_date_str: String,
) -> Result<Vec<ViolationReportItem>, String> {
    println!("[TAURI_CMD] get_violations_report called with startDate: {}, endDate: {}", start_date_str, end_date_str);

    let start_date = NaiveDate::parse_from_str(&start_date_str, "%Y-%m-%d")
        .map_err(|e| {
            eprintln!("[TAURI_CMD_ERR] Invalid start_date format: {}", e);
            format!("Geçersiz başlangıç tarihi formatı: {}", e)
        })?;
    let end_date = NaiveDate::parse_from_str(&end_date_str, "%Y-%m-%d")
        .map_err(|e| {
            eprintln!("[TAURI_CMD_ERR] Invalid end_date format: {}", e);
            format!("Geçersiz bitiş tarihi formatı: {}", e)
        })?;

    if start_date > end_date {
        eprintln!("[TAURI_CMD_ERR] Start date cannot be after end date.");
        return Err("Başlangıç tarihi bitiş tarihinden sonra olamaz.".to_string());
    }

    let all_convicts_result = db_get_convicts(&db);
    if let Err(e) = &all_convicts_result {
        eprintln!("[TAURI_CMD_ERR] Error getting all convicts: {}", e);
    }
    let all_convicts = all_convicts_result.map_err(|e| e.to_string())?;
    println!("[TAURI_CMD_DEBUG] Total convicts to process: {}", all_convicts.len());
    let mut violations: Vec<ViolationReportItem> = Vec::new();

    for (convict_idx, convict) in all_convicts.iter().enumerate() {
        println!("[TAURI_CMD_DEBUG] Processing convict {}/{}: ID {}", convict_idx + 1, all_convicts.len(), convict.id);
        if !convict.is_active {
            println!("[TAURI_CMD_DEBUG] Convict {} (ID: {}) is not active. Skipping.", convict_idx + 1, convict.id);
            continue;
        }

        println!("[TAURI_CMD_DEBUG] Getting signature periods for convict ID {}", convict.id);
        let signature_periods_result = get_signature_periods_by_convict(&db, convict.id);
        if let Err(e) = &signature_periods_result {
            eprintln!("[TAURI_CMD_ERR] Error getting signature periods for convict {}: {}", convict.id, e);
        }
        let signature_periods = signature_periods_result.map_err(|e| e.to_string())?;
        println!("[TAURI_CMD_DEBUG] Found {} signature periods for convict ID {}", signature_periods.len(), convict.id);

        let mut current_date_iter = start_date;
        while current_date_iter <= end_date {
            let date_str_for_check = current_date_iter.format("%Y-%m-%d").to_string();
            println!("[TAURI_CMD_DEBUG] Convict ID {}, Date: {}", convict.id, date_str_for_check);
            
            let mut due_today = false;
            let mut frequency_text_for_due_day = "".to_string();

            for period in &signature_periods {
                if is_signature_due_on_date(current_date_iter, period) {
                    due_today = true;
                    frequency_text_for_due_day = get_frequency_text_for_period(period);
                    break;
                }
            }

            if due_today {
                println!("[TAURI_CMD_DEBUG] Convict ID {}, Date: {}. Due today. Checking signature and exemptions.", convict.id, date_str_for_check);
                
                // Check if convict has active exemptions on this date
                let exemptions_result = get_active_exemptions_by_convict_and_date(&db, convict.id, &date_str_for_check);
                if let Err(e) = &exemptions_result {
                    eprintln!("[TAURI_CMD_ERR] Error checking exemptions for convict {} on date {}: {}", convict.id, date_str_for_check, e);
                }
                let active_exemptions = exemptions_result.map_err(|e| format!("Muafiyet kontrol hatası: {}", e))?;
                
                if !active_exemptions.is_empty() {
                    println!("[TAURI_CMD_DEBUG] Convict ID {}, Date: {}. Has active exemption(s). Skipping violation check.", convict.id, date_str_for_check);
                    // Skip violation check if there are active exemptions
                } else {
                    let signed_today_result = check_signature_exists(&db, convict.id, &date_str_for_check);
                    if let Err(e) = &signed_today_result {
                         eprintln!("[TAURI_CMD_ERR] Error checking signature for convict {} on date {}: {}", convict.id, date_str_for_check, e);
                    }
                    let signed_today = signed_today_result
                        .map_err(|e| format!("İmza kontrol hatası: {}", e))?;
                    println!("[TAURI_CMD_DEBUG] Convict ID {}, Date: {}. Signed: {}.", convict.id, date_str_for_check, signed_today);
                    
                    if !signed_today {
                        println!("[TAURI_CMD_DEBUG] Violation found for Convict ID {}, Date: {}", convict.id, date_str_for_check);
                        violations.push(ViolationReportItem {
                            convict: convict.clone(),
                            violation_date: date_str_for_check,
                            expected_frequency: frequency_text_for_due_day,
                        });
                    }
                }
            }
            
            match current_date_iter.succ_opt() {
                Some(next_day) => {
                    current_date_iter = next_day;
                }
                None => { 
                    eprintln!("[TAURI_CMD_WARN] Could not determine next day for {}. Breaking date loop for convict {}.", current_date_iter, convict.id);
                    break; 
                }
            }
        }
        println!("[TAURI_CMD_DEBUG] Finished processing dates for convict ID {}", convict.id);
    }
    violations.sort_by(|a, b| b.violation_date.cmp(&a.violation_date));
    println!("[TAURI_CMD] get_violations_report finished. Found {} violations.", violations.len());
    Ok(violations)
}

#[tauri::command]
pub async fn get_expected_signatures_for_today(
    db: State<'_, DatabaseConnection>,
) -> Result<Vec<Convict>, String> {
    // Tüm beklenen imzaları al ve sadece imza atmamış olanları filtrele
    let all_expected = get_all_expected_signatures_for_today_db(&db)
        .map_err(|e| format!("Failed to get expected signatures for today: {}", e))?;
    
    // ExpectedSignature'ları Convict'e dönüştür
    let unsigned_convicts: Vec<Convict> = all_expected
        .into_iter()
        .filter(|s| !s.has_signed)
        .map(|s| Convict {
            id: s.convict_id,
            tc_no: s.tc_no,
            first_name: s.first_name,
            last_name: s.last_name,
            supervision_start_date: String::new(), // Bu alanlar frontend'de kullanılmıyor
            supervision_end_date: String::new(),   // Bu alanlar frontend'de kullanılmıyor
            phone_number: None,
            relative_phone_number: None,
            address: None,
            file_number: None,
            is_active: true,
            notes: None,
            created_at: None,
            updated_at: None,
        })
        .collect();
    
    Ok(unsigned_convicts)
}

#[tauri::command]
pub async fn get_completed_signatures_for_today(
    db: State<'_, DatabaseConnection>,
) -> Result<Vec<CompletedSignature>, String> {
    let conn_guard = db.lock().map_err(|e| e.to_string())?;
    let conn = &*conn_guard;
    
    let today = chrono::Utc::now().date_naive().format("%Y-%m-%d").to_string();
    
    let mut stmt = conn.prepare(
        "SELECT c.id, c.tc_no, c.first_name, c.last_name, s.signature_time, s.signature_date 
         FROM signatures s 
         JOIN convicts c ON s.convict_id = c.id 
         WHERE s.signature_date = ?1 
         ORDER BY s.signature_time"
    ).map_err(|e| e.to_string())?;
    
    let completed_signatures = stmt.query_map([&today], |row| {
        Ok(CompletedSignature {
            convict_id: row.get(0)?,
            tc_no: row.get(1)?,
            first_name: row.get(2)?,
            last_name: row.get(3)?,
            signature_time: row.get(4)?,
            signature_date: row.get(5)?,
        })
    }).map_err(|e| e.to_string())?;
    
    let mut result = Vec::new();
    for signature in completed_signatures {
        result.push(signature.map_err(|e| e.to_string())?);
    }
    
    Ok(result)
}

#[tauri::command]
pub async fn get_all_expected_signatures_for_today(
    db: State<'_, DatabaseConnection>,
) -> Result<Vec<ExpectedSignature>, String> {
    get_all_expected_signatures_for_today_db(&db)
        .map_err(|e| format!("Failed to get all expected signatures for today: {}", e))
}

#[tauri::command]
pub async fn get_violations_for_last_30_days(
    db: State<'_, DatabaseConnection>,
) -> Result<Vec<ViolationRecord>, String> {
    get_violations_for_last_30_days_db(&db)
        .map_err(|e| format!("Failed to get violations for last 30 days: {}", e))
}

#[tauri::command]
pub async fn save_binary_file(file_path: String, data: Vec<u8>) -> Result<(), String> {
    fs::write(file_path, data).map_err(|e| e.to_string())
}

// Exemption management commands
#[tauri::command]
pub async fn create_new_exemption(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    exemption: InsertExemption,
) -> Result<Exemption, String> {
    match create_exemption(&db, exemption) {
        Ok(new_exemption) => {
            // Emit real-time event for exemption creation
            emit_data_change(&app, "created", "exemptions", Some(new_exemption.id));
            Ok(new_exemption)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn get_convict_exemptions(
    db: State<'_, DatabaseConnection>,
    convict_id: i32,
) -> Result<Vec<Exemption>, String> {
    get_exemptions_by_convict(&db, convict_id).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_active_exemptions_for_date(
    db: State<'_, DatabaseConnection>,
    convict_id: i32,
    date: String,
) -> Result<Vec<Exemption>, String> {
    get_active_exemptions_by_convict_and_date(&db, convict_id, &date).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_exemption_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
    exemption: InsertExemption,
) -> Result<Exemption, String> {
    match update_exemption(&db, id, exemption) {
        Ok(updated_exemption) => {
            // Emit real-time event for exemption update
            emit_data_change(&app, "updated", "exemptions", Some(id));
            Ok(updated_exemption)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn delete_exemption_data(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    id: i32,
) -> Result<bool, String> {
    match delete_exemption(&db, id) {
        Ok(success) => {
            if success {
                // Emit real-time event for exemption deletion
                emit_data_change(&app, "deleted", "exemptions", Some(id));
            }
            Ok(success)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn bulk_create_signature_periods_cmd(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    periods: Vec<InsertSignaturePeriod>,
) -> Result<Vec<SignaturePeriod>, String> {
    match crate::database::bulk_create_signature_periods_with_transaction(&db, periods) {
        Ok(periods) => {
            // Emit real-time event for bulk signature period creation
            emit_data_change(&app, "bulk_created", "signature_periods", None);
            Ok(periods)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn bulk_create_exemptions_cmd(
    app: AppHandle,
    db: State<'_, DatabaseConnection>,
    exemptions: Vec<InsertExemption>,
) -> Result<Vec<Exemption>, String> {
    match crate::database::bulk_create_exemptions_with_transaction(&db, exemptions) {
        Ok(exemptions) => {
            // Emit real-time event for bulk exemption creation
            emit_data_change(&app, "bulk_created", "exemptions", None);
            Ok(exemptions)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn get_convict_full_details_cmd(
    db: State<'_, DatabaseConnection>,
) -> Result<Vec<ConvictFullDetails>, String> {
    get_convict_full_details(&db).map_err(|e| e.to_string())
}
