# Windows Build Guide for EITS

This guide explains how to build the EITS application for Windows platform.

## Prerequisites

### 1. Install Required Tools

#### Node.js and pnpm
```bash
# Install Node.js (v18 or later)
# Download from: https://nodejs.org/

# Install pnpm
npm install -g pnpm
```

#### Rust and Tauri CLI
```bash
# Install Rust
# Download from: https://rustup.rs/

# Set MSVC toolchain as default (required for Tauri)
rustup default stable-msvc

# Install Tauri CLI
cargo install tauri-cli
```

#### Visual Studio Build Tools
- Install Visual Studio 2022 with C++ build tools
- Or install "Build Tools for Visual Studio 2022"
- Ensure you have the Windows 10/11 SDK

### 2. WebView2 Runtime
Windows 10/11 usually has WebView2 pre-installed. If not:
- Download from: https://developer.microsoft.com/en-us/microsoft-edge/webview2/

## Build Commands

### Standard Windows Build
```bash
# Install dependencies
pnpm install

# Build for Windows (creates MSI and NSIS installers)
pnpm run build:windows

# Build for specific architecture
pnpm run build:windows-x64    # 64-bit Intel/AMD
pnpm run build:windows-x86    # 32-bit Intel/AMD
pnpm run build:windows-arm64  # ARM64 (Surface Pro X, etc.)
```

### Offline Windows Build (Recommended)
```bash
# Build with offline WebView2 installer (~127MB larger but no internet required)
pnpm run build:windows-offline

# Build offline for specific architecture
pnpm run build:windows-offline-x64    # 64-bit Intel/AMD with offline WebView2

# Use the automated script
./build-windows-offline.bat
```

### Development Build
```bash
# Run in development mode
pnpm run tauri dev
```

## Build Output

After successful build, you'll find the installers in:
```
src-tauri/target/release/bundle/
├── msi/           # MSI installer files
└── nsis/          # NSIS installer files
```

## Configuration

The application is configured for Windows with:

- **Bundle Types**: MSI and NSIS installers
- **WebView2**: Offline installer (~127MB) - No internet required during installation
- **Languages**: English (en-US) and Turkish (tr-TR)
- **Window Settings**:
  - Starts maximized (fullscreen mode)
  - Custom title bar with overlay style
  - Minimum size: 800x600
  - Default size: 1200x800

## WebView2 Offline Installation

The application uses `offlineInstaller` mode which:
- Embeds the complete WebView2 runtime (~127MB)
- Allows installation without internet connection
- Ensures consistent WebView2 version across all installations
- Provides better reliability for enterprise environments

## Troubleshooting

### Common Issues

1. **Build fails with "MSVC not found"**
   ```bash
   rustup default stable-msvc
   ```

2. **WebView2 errors**
   - Ensure WebView2 runtime is installed
   - Check Windows version compatibility

3. **Permission errors during build**
   - Run terminal as Administrator
   - Check antivirus software isn't blocking the build

4. **Large bundle size**
   - The application includes a full SQLite database
   - Bundle size is optimized for offline operation

### Build Environment Variables

```bash
# For FIPS compliance (if required)
set TAURI_FIPS_COMPLIANT=true

# For debugging
set RUST_BACKTRACE=1
```

## Signing (Optional)

For production releases, you may want to sign the executables:

1. Obtain a code signing certificate
2. Configure in `src-tauri/tauri.conf.json`:
   ```json
   {
     "bundle": {
       "windows": {
         "certificateThumbprint": "YOUR_CERT_THUMBPRINT",
         "digestAlgorithm": "sha256",
         "timestampUrl": "http://timestamp.comodoca.com"
       }
     }
   }
   ```

## GitHub Actions (Alternative)

For automated builds, see `.github/workflows/build-windows.yml` for a complete CI/CD setup.
