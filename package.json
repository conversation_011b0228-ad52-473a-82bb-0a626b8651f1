{"name": "eits", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:reset": "rm -f database.sqlite && npm run db:migrate && npm run db:seed", "backend:build": "cd src-tauri && cargo build", "backend:check": "cd src-tauri && cargo check", "build:windows": "pnpm tauri build --config src-tauri/tauri.windows.conf.json", "build:windows-x64": "pnpm tauri build --config src-tauri/tauri.windows.conf.json --target x86_64-pc-windows-msvc", "build:windows-x86": "pnpm tauri build --config src-tauri/tauri.windows.conf.json --target i686-pc-windows-msvc", "build:windows-arm64": "pnpm tauri build --config src-tauri/tauri.windows.conf.json --target aarch64-pc-windows-msvc", "build:windows-offline": "pnpm tauri build --config src-tauri/tauri.offline.conf.json", "build:windows-offline-x64": "pnpm tauri build --config src-tauri/tauri.offline.conf.json --target x86_64-pc-windows-msvc", "build:windows-cross": "pnpm tauri build --runner cargo-xwin --target x86_64-pc-windows-msvc"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.60.5", "@tanstack/react-table": "^8.21.3", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-opener": "^2", "@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.24.2", "zod-validation-error": "^3.4.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.28.0", "@tailwindcss/typography": "^0.5.15", "@tauri-apps/api": "^2.5.0", "@tauri-apps/cli": "^2", "@types/connect-pg-simple": "^7.0.3", "@types/jspdf": "^1.3.3", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@types/xlsx": "^0.0.36", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "typescript-eslint": "^8.33.0", "vite": "^5.4.14"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}