#!/bin/bash

# EITS Contact Fields - Comprehensive Test Script
# This script helps verify the Excel import/export functionality

echo "=== EITS Contact Fields Test Script ==="
echo "Date: $(date)"
echo

# Test 1: Database Schema Verification
echo "1. TESTING DATABASE SCHEMA..."
echo "Checking if contact fields exist in convicts table:"
sqlite3 database.sqlite ".schema convicts" | grep -E "(phone_number|relative_phone_number|address)"
echo

# Test 2: Current Data Verification
echo "2. CURRENT CONTACT DATA IN DATABASE..."
echo "Records with contact information:"
sqlite3 database.sqlite -header -column "SELECT tc_no, first_name, last_name, phone_number, relative_phone_number, address FROM convicts WHERE phone_number IS NOT NULL OR relative_phone_number IS NOT NULL OR address IS NOT NULL;"
echo

# Test 3: Test File Verification
echo "3. TEST FILE VERIFICATION..."
echo "CSV test file content:"
head -3 test_contact_fields.csv
echo

echo "Excel test file exists:"
ls -la test_contact_fields.xlsx
echo

# Test 4: Application Status
echo "4. APPLICATION STATUS..."
echo "Checking if application is running:"
curl -s http://localhost:1420 > /dev/null && echo "✅ Application is accessible at localhost:1420" || echo "❌ Application not accessible"
echo

# Test 5: TypeScript Compilation Test
echo "5. TYPESCRIPT COMPILATION TEST..."
echo "Running TypeScript check:"
npm run type-check 2>&1 | tail -5
echo

echo "=== MANUAL TESTING INSTRUCTIONS ==="
echo
echo "Please perform the following tests in the browser at http://localhost:1420:"
echo
echo "A. FORM INPUT TEST:"
echo "   1. Click 'Hükümlü Ekle' (Add Convict)"
echo "   2. Fill required fields: TC=11111111111, Name=TestUser, Surname=Manual"
echo "   3. Add contact info:"
echo "      - Phone: +90 ************"
echo "      - Relative Phone: +90 ************"
echo "      - Address: Test Mahallesi, Test Sokak No:1, Test/Şehir"
echo "   4. Set period: Start=2024-12-18, End=2025-12-18"
echo "   5. Submit and verify success"
echo
echo "B. EXCEL IMPORT TEST:"
echo "   1. Go to import section"
echo "   2. Select 'test_contact_fields.xlsx'"
echo "   3. Verify preview shows contact columns"
echo "   4. Import and check success message"
echo
echo "C. EXCEL EXPORT TEST:"
echo "   1. Go to export section"  
echo "   2. Export convicts data"
echo "   3. Open downloaded file"
echo "   4. Verify contact field columns exist"
echo
echo "D. EDIT CONVICT TEST:"
echo "   1. Select existing convict"
echo "   2. Click edit"
echo "   3. Modify contact information"
echo "   4. Save and verify changes"
echo
echo "E. SIGNATURE RECORD TEST:"
echo "   1. Navigate to signature recording"
echo "   2. Select convict with contact info"
echo "   3. Verify contact info displays"
echo
echo "=== POST-TEST VERIFICATION ==="
echo "After manual testing, run this command to verify data:"
echo "sqlite3 database.sqlite \"SELECT COUNT(*) as total_contacts FROM convicts WHERE phone_number IS NOT NULL OR relative_phone_number IS NOT NULL OR address IS NOT NULL;\""
