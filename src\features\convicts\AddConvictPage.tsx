import { useLocation } from 'wouter';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { createConvict, createSignaturePeriod } from '@/lib/tauri-api';
import AddConvictWithPeriodsForm from './AddConvictWithPeriodsForm';
import type { InsertConvict, InsertSignaturePeriod } from '@shared/schema';

export default function AddConvictPage() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const createConvictWithPeriodsMutation = useMutation({
    mutationFn: async ({ convictData, periodsData }: { 
      convictData: InsertConvict; 
      periodsData: Omit<InsertSignaturePeriod, 'convict_id'>[] 
    }) => {
      // First create the convict
      const newConvict = await createConvict(convictData);
      
      // Then create signature periods if any
      if (periodsData.length > 0) {
        const periodPromises = periodsData.map(periodData => 
          createSignaturePeriod({
            ...periodData,
            convict_id: newConvict.id,
          })
        );
        await Promise.all(periodPromises);
      }
      
      return newConvict;
    },
    onSuccess: (newConvict) => {
      toast({
        title: 'Başarılı',
        description: 'Hükümlü ve imza periyotları başarıyla eklendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['convicts'] });
      queryClient.invalidateQueries({ queryKey: ['signature-periods', newConvict.id] });
      setLocation('/convicts');
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Hükümlü eklenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (convictData: InsertConvict, periodsData: Omit<InsertSignaturePeriod, 'convict_id'>[]) => {
    createConvictWithPeriodsMutation.mutate({ convictData, periodsData });
  };

  const handleCancel = () => {
    setLocation('/convicts');
  };

  return (
    <div className="windows-content">
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="windows-title">Yeni Hükümlü Ekle</h1>
            <p className="windows-subtitle">
              Sisteme yeni hükümlü kaydı oluşturun, bilgilerini girin ve imza periyotlarını belirleyin
            </p>
          </div>
          <div className="hidden md:flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-200 border border-gray-400 flex items-center justify-center">
              <span className="text-sm">👤</span>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4">
        <AddConvictWithPeriodsForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createConvictWithPeriodsMutation.isPending}
        />
      </div>
    </div>
  );
}
