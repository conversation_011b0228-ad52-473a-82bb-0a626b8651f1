import * as React from "react";
import * as RadixDialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface WindowsDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  title: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  contentClassName?: string;
}

export function WindowsDialog({
  open,
  onOpenChange,
  title,
  icon,
  children,
  footer,
  className,
  contentClassName,
}: WindowsDialogProps) {
  return (
    <RadixDialog.Root open={open} onOpenChange={onOpenChange}>
      <RadixDialog.Portal>
        <RadixDialog.Overlay className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50" />
        <RadixDialog.Content
          className={cn(
            "fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-50 windows-dialog min-w-[450px] max-w-md",
            className
          )}
        >
          <div className="windows-dialog-title">
            <div className="flex items-center gap-2">
              {icon && <span>{icon}</span>}
              <span className="text-xs">{title}</span>
            </div>
            <RadixDialog.Close className="h-5 w-5 flex items-center justify-center rounded hover:bg-white/20">
              <X className="h-3 w-3" />
            </RadixDialog.Close>
          </div>
          
          <div className={cn("p-6", contentClassName)}>
            {children}
          </div>
          
          {footer && (
            <div className="px-6 py-4 bg-gray-100 border-t border-gray-300 flex justify-end gap-2">
              {footer}
            </div>
          )}
        </RadixDialog.Content>
      </RadixDialog.Portal>
    </RadixDialog.Root>
  );
}

export function WindowsConfirmDialog({
  open,
  onOpenChange,
  title,
  icon,
  children,
  onConfirm,
  onCancel,
  confirmText = "OK",
  cancelText = "İptal",
  isDestructive = false,
  isLoading = false,
  className,
}: WindowsDialogProps & {
  onConfirm: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  isDestructive?: boolean;
  isLoading?: boolean;
}) {
  const handleConfirm = () => {
    onConfirm();
    if (onOpenChange) onOpenChange(false);
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
    if (onOpenChange) onOpenChange(false);
  };

  return (
    <WindowsDialog
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      icon={icon}
      className={className}
      footer={
        <>
          <button
            onClick={handleCancel}
            className="windows-button h-6 px-3 py-1 text-xs"
            disabled={isLoading}
          >
            {cancelText}
          </button>
          <button
            onClick={handleConfirm}
            className={cn(
              "windows-button h-6 px-3 py-1 text-xs",
              isDestructive
                ? "bg-red-600 text-white hover:bg-red-700"
                : "bg-blue-600 text-white hover:bg-blue-700"
            )}
            disabled={isLoading}
          >
            {isLoading ? "İşleniyor..." : confirmText}
          </button>
        </>
      }
    >
      {children}
    </WindowsDialog>
  );
}
