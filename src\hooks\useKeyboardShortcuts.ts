import { useEffect, useCallback } from 'react';
import { useLocation } from 'wouter';
import { useUIStore } from '@/store/uiStore';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: () => void;
  description: string;
}

const useKeyboardShortcuts = () => {
  const [, setLocation] = useLocation();
  const { toggleToolbar, toggleStatusBar } = useUIStore();

  const shortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: 'Home',
      ctrlKey: true,
      action: () => setLocation('/dashboard'),
      description: 'Ana sayfaya git'
    },
    {
      key: 'n',
      ctrlKey: true,
      action: () => setLocation('/convicts/new'),
      description: '<PERSON><PERSON> hükü<PERSON>lü ekle'
    },
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="ara"], input[placeholder*="Ara"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
          searchInput.select();
        }
      },
      description: 'Arama kutusuna odaklan'
    },
    {
      key: 'f',
      ctrlKey: true,
      shiftKey: true,
      action: () => {
        console.log('Filter shortcut activated');
        // Focus filter controls or show filter dialog
      },
      description: 'Filtreleme seçeneklerini aç'
    },
    // System shortcuts
    {
      key: 'F1',
      action: () => setLocation('/help/usage-guide'),
      description: 'Kullanım kılavuzunu aç'
    },
    {
      key: 'F5',
      action: () => window.location.reload(),
      description: 'Sayfayı yenile'
    },
    {
      key: 'r',
      ctrlKey: true,
      action: () => window.location.reload(),
      description: 'Sayfayı yenile'
    },
    {
      key: 'p',
      ctrlKey: true,
      action: () => window.print(),
      description: 'Sayfayı yazdır'
    },
    // View shortcuts
    {
      key: 't',
      ctrlKey: true,
      action: toggleToolbar,
      description: 'Araç çubuğunu göster/gizle'
    },
    {
      key: 'b',
      ctrlKey: true,
      action: toggleStatusBar,
      description: 'Durum çubuğunu göster/gizle'
    },
    // Convict management shortcuts
    {
      key: 'l',
      ctrlKey: true,
      action: () => setLocation('/convicts'),
      description: 'Hükümlü listesini aç'
    },
    {
      key: 's',
      ctrlKey: true,
      action: () => setLocation('/signatures/record'),
      description: 'İmza kaydı sayfasını aç'
    },
    // Reports shortcuts
    {
      key: 'd',
      ctrlKey: true,
      altKey: true,
      action: () => setLocation('/reports/daily'),
      description: 'Günlük raporu aç'
    },
    {
      key: 'v',
      ctrlKey: true,
      altKey: true,
      action: () => setLocation('/reports/violations'),
      description: 'İhlal raporu aç'
    },
    // Admin shortcuts
    {
      key: 'u',
      ctrlKey: true,
      altKey: true,
      action: () => setLocation('/admin/users'),
      description: 'Kullanıcı yönetimi'
    },
    {
      key: ',',
      ctrlKey: true,
      action: () => setLocation('/admin/settings'),
      description: 'Sistem ayarları'
    }
  ];

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const matchedShortcut = shortcuts.find(shortcut => {
      const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatch = shortcut.ctrlKey === undefined || shortcut.ctrlKey === (event.ctrlKey || event.metaKey);
      const altMatch = shortcut.altKey === undefined || shortcut.altKey === event.altKey;
      const shiftMatch = shortcut.shiftKey === undefined || shortcut.shiftKey === event.shiftKey;
      
      return keyMatch && ctrlMatch && altMatch && shiftMatch;
    });

    if (matchedShortcut) {
      event.preventDefault();
      matchedShortcut.action();
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { shortcuts };
};

export default useKeyboardShortcuts;
