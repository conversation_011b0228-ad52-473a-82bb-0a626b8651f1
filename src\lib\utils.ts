import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: string | Date, options?: Intl.DateTimeFormatOptions): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    ...options,
  });
}

export function formatDateTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
}

export function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0];
}

export function getCurrentTime(): string {
  const now = new Date();
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
}

export function validateTcNo(tcNo: string): boolean {
  if (!/^\d{11}$/.test(tcNo)) return false;
  
  const digits = tcNo.split('').map(Number);
  const sum1 = digits[0] + digits[2] + digits[4] + digits[6] + digits[8];
  const sum2 = digits[1] + digits[3] + digits[5] + digits[7];
  
  if ((sum1 * 7 - sum2) % 10 !== digits[9]) return false;
  if ((sum1 + sum2 + digits[9]) % 10 !== digits[10]) return false;
  
  return true;
}

const weekDayBackendToLabel: { [key: string]: string } = {
  MONDAY: 'Pazartesi',
  TUESDAY: 'Salı',
  WEDNESDAY: 'Çarşamba',
  THURSDAY: 'Perşembe',
  FRIDAY: 'Cuma',
  SATURDAY: 'Cumartesi',
  SUNDAY: 'Pazar',
};

export function getFrequencyText(frequencyType: string, frequencyValue: string): string {
  if (!frequencyValue) return 'Sıklık belirtilmemiş'; // frequencyValue boşsa erken çıkış

  try {
    switch (frequencyType) {
      case 'WEEKLY':
        const dayNames = frequencyValue.split(',')
          .map(dayKey => weekDayBackendToLabel[dayKey.trim()] || dayKey.trim())
          .join(', ');
        return `Haftalık: ${dayNames}`;
      case 'X_DAYS':
        // frequencyValue burada "3" gibi bir string olmalı
        return `${frequencyValue} Günde Bir`;
      case 'MONTHLY_SPECIFIC':
        // frequencyValue burada "1,15,30" gibi bir string olmalı
        return `Aylık: ${frequencyValue} günleri`;
      default:
        return 'Bilinmeyen sıklık';
    }
  } catch (error) {
    console.error("Error parsing frequency data:", error);
    return 'Geçersiz sıklık verisi (işlenemedi)';
  }
}
