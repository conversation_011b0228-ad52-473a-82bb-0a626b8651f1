import { ReactNode } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from './LoadingSpinner';
import EmptyState from './EmptyState';

interface Column<T> {
  key: keyof T | string;
  label: string;
  render?: (item: T) => ReactNode;
  sortable?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
  };
  emptyMessage?: string;
  actions?: (item: T) => ReactNode;
  // New props for selection
  selectable?: boolean;
  selectedItems?: T[];
  onSelectionChange?: (selectedItems: T[]) => void;
  getItemId?: (item: T) => string | number;
}

export default function DataTable<T = Record<string, unknown>>({
  data,
  columns,
  loading = false,
  pagination,
  emptyMessage = "Kayıt bulunamadı",
  actions,
  selectable,
  selectedItems,
  onSelectionChange,
  getItemId,
}: DataTableProps<T>) {
  if (loading) {
    return (
      <div className="windows-card">
        <div className="p-8 flex flex-col items-center justify-center">
          <LoadingSpinner size="lg" />
          <p className="mt-3 text-gray-600 text-sm">Veriler yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="windows-card">
        <div className="p-12">
          <EmptyState message={emptyMessage} />
        </div>
      </div>
    );
  }

  const renderCell = (item: T, column: Column<T>) => {
    if (column.render) {
      return column.render(item);
    }
    
    const value = item[column.key as keyof T];
    return value?.toString() || '-';
  };

  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      if (checked) {
        onSelectionChange(data);
      } else {
        onSelectionChange([]);
      }
    }
  };

  const handleSelectItem = (checked: boolean, item: T) => {
    if (onSelectionChange) {
      const newSelectedItems = checked 
        ? [...(selectedItems || []), item] 
        : selectedItems?.filter(i => getItemId ? getItemId(i) !== getItemId(item) : i !== item);

      onSelectionChange(newSelectedItems || []);
    }
  };

  const isItemSelected = (item: T): boolean => {
    if (!selectedItems || selectedItems.length === 0) return false;
    return selectedItems.some(i => getItemId ? getItemId(i) === getItemId(item) : i === item);
  };

  return (
    <div className="windows-card overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-100 border-b border-gray-300">
              {selectable && (
                <TableHead className="px-3 py-2 text-xs font-medium text-gray-800">
                  <Checkbox
                    checked={selectedItems?.length === data.length && data.length > 0}
                    onCheckedChange={(checked) => handleSelectAll(!!checked)}
                    aria-label="Tümünü Seç"
                  />
                </TableHead>
              )}
              {columns.map((column) => (
                <TableHead 
                  key={column.key.toString()} 
                  className="px-3 py-2 text-xs font-medium text-gray-800"
                >
                  {column.label}
                </TableHead>
              ))}
              {actions && (
                <TableHead className="px-3 py-2 text-xs font-medium text-gray-800 text-right">
                  İşlemler
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item, index) => (
              <TableRow 
                key={index} 
                className="hover:bg-blue-50 border-b border-gray-200 last:border-b-0"
              >
                {selectable && (
                  <TableCell className="px-3 py-2 text-xs text-gray-700">
                    <Checkbox
                      checked={isItemSelected(item)}
                      onCheckedChange={(checked) => handleSelectItem(!!checked, item)}
                      aria-label="Seç"
                    />
                  </TableCell>
                )}
                {columns.map((column) => (
                  <TableCell 
                    key={column.key.toString()}
                    className="px-3 py-2 text-xs text-gray-700"
                  >
                    {renderCell(item, column)}
                  </TableCell>
                ))}
                {actions && (
                  <TableCell className="px-3 py-2 text-xs text-gray-700 text-right">
                    {actions(item)}
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {pagination && (
        <div className="bg-gray-50 px-3 py-2 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <p className="text-xs text-gray-700">
                Gösterilen:{" "}
                <span className="font-medium">
                  {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
                </span>{" "}
                -{" "}
                <span className="font-medium">
                  {Math.min(
                    pagination.currentPage * pagination.itemsPerPage,
                    pagination.totalItems
                  )}
                </span>{" "}
                / <span className="font-medium">{pagination.totalItems}</span>
              </p>
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="windows"
                size="sm"
                onClick={() => pagination.onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage <= 1}
              >
                <ChevronLeftIcon className="w-3 h-3 mr-1" />
                Önceki
              </Button>
              
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={pagination.currentPage === page ? "windows-primary" : "windows"}
                      size="sm"
                      onClick={() => pagination.onPageChange(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              
              <Button
                variant="windows"
                size="sm"
                onClick={() => pagination.onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage >= pagination.totalPages}
              >
                Sonraki
                <ChevronRightIcon className="w-3 h-3 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
