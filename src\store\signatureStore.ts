import { create } from 'zustand';
import type { Convict } from '@shared/schema';

interface SignatureState {
  searchQuery: string;
  foundConvict: Convict | null;
  signatureStatus: {
    isRequired: boolean;
    alreadySigned: boolean;
    signatureTime?: string;
    hasActiveExemptions?: boolean;
    exemptions?: Array<{
      type: string;
      description?: string;
    }>;
    timeValidation?: {
      isValid: boolean;
      errors: string[];
      activePeriods?: Array<{
        id: number;
        timeRestriction: string;
      }>;
    };
  };
  setSearchQuery: (query: string) => void;
  setFoundConvict: (convict: Convict | null) => void;
  setSignatureStatus: (status: SignatureState['signatureStatus']) => void;
  clearSearch: () => void;
}

export const useSignatureStore = create<SignatureState>((set) => ({
  searchQuery: '',
  foundConvict: null,
  signatureStatus: {
    isRequired: false,
    alreadySigned: false,
  },
  setSearchQuery: (query) => set({ searchQuery: query }),
  setFoundConvict: (convict) => set({ foundConvict: convict }),
  setSignatureStatus: (status) => set({ signatureStatus: status }),
  clearSearch: () => set({
    searchQuery: '',
    foundConvict: null,
    signatureStatus: {
      isRequired: false,
      alreadySigned: false,
      hasActiveExemptions: false,
      exemptions: [],
      timeValidation: undefined,
    },
  }),
}));
