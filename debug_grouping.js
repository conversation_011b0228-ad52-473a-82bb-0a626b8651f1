// Direct test of the grouping functionality
const groupSignaturePeriods = (periods) => {
  const groups = new Map();
  
  console.log('Input periods:', periods);
  
  // Group periods by date range, type, and time
  periods.forEach(period => {
    const key = `${period.start_date}_${period.end_date}_${period.frequency_type}_${period.time_start || ''}_${period.time_end || ''}`;
    console.log('Grouping key for period:', key, period);
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(period);
  });
  
  console.log('Groups formed:', groups);
  
  // Convert groups to consolidated periods
  const groupedPeriods = Array.from(groups.values()).map(groupPeriods => {
    console.log('Processing group:', groupPeriods);
    
    // Use the first period as base
    const basePeriod = groupPeriods[0];
    
    // Merge frequency values based on type
    let mergedFrequencyValue = '';
    if (basePeriod.frequency_type === 'WEEKLY') {
      // Collect all weekly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      mergedFrequencyValue = Array.from(allDays).join(',');
      console.log('WEEKLY merged value:', mergedFrequencyValue);
    } else if (basePeriod.frequency_type === 'MONTHLY_SPECIFIC') {
      // Collect all monthly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      // Sort numerically for monthly days
      const sortedDays = Array.from(allDays).sort((a, b) => parseInt(a) - parseInt(b));
      mergedFrequencyValue = sortedDays.join(',');
      console.log('MONTHLY_SPECIFIC merged value:', mergedFrequencyValue);
    } else {
      // For X_DAYS type, use the first period's value
      mergedFrequencyValue = basePeriod.frequency_value;
      console.log('X_DAYS value:', mergedFrequencyValue);
    }
    
    // Return a merged period
    const result = {
      ...basePeriod,
      frequency_value: mergedFrequencyValue
    };
    console.log('Merged period result:', result);
    return result;
  });
  
  console.log('Final grouped periods:', groupedPeriods);
  return groupedPeriods;
};

// Test data for convict 108
const testPeriodsConvict108 = [
  {
    id: 1,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'TUESDAY',
    time_start: '09:00',
    time_end: '17:00'
  },
  {
    id: 2,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'THURSDAY',
    time_start: '09:00',
    time_end: '17:00'
  },
  {
    id: 3,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'SATURDAY',
    time_start: '09:00',
    time_end: '17:00'
  },
  {
    id: 4,
    start_date: '2025-08-03',
    end_date: '2025-12-31',
    frequency_type: 'MONTHLY_SPECIFIC',
    frequency_value: '1,15,30',
    time_start: '09:00',
    time_end: '17:00'
  }
];

console.log('🧪 Testing grouping for Convict 108');
console.log('====================================');
const grouped108 = groupSignaturePeriods(testPeriodsConvict108);

console.log('\n📊 Summary:');
console.log(`Original periods: ${testPeriodsConvict108.length}`);
console.log(`Grouped periods: ${grouped108.length}`);

console.log('\n📋 Expected: 2 groups');
console.log('1. Weekly group: TUESDAY,THURSDAY,SATURDAY (2025-06-03 to 2025-08-03)');
console.log('2. Monthly group: 1,15,30 (2025-08-03 to 2025-12-31)');
