const Database = require('better-sqlite3');
const path = require('path');

// Database connection
const dbPath = path.join(__dirname, 'database.sqlite');
console.log('Database path:', dbPath);

let db;
try {
  db = Database(dbPath);
  console.log('✅ Database connection successful');
} catch (error) {
  console.error('❌ Database connection failed:', error.message);
  process.exit(1);
}

// Date generation logic (mirrored from TypeScript)
function generateSignatureDates(periods, maxDates = 12) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const allDates = [];
  const dayNames = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
  
  for (const period of periods) {
    const periodStart = new Date(period.start_date);
    const periodEnd = new Date(period.end_date);
    periodStart.setHours(0, 0, 0, 0);
    periodEnd.setHours(0, 0, 0, 0);
    
    const searchStartDate = new Date(Math.max(today.getTime(), periodStart.getTime()));
    
    if (period.frequency_type === 'WEEKLY') {
      const targetDay = period.frequency_value.toUpperCase();
      const targetDayIndex = dayNames.indexOf(targetDay);
      
      if (targetDayIndex === -1) continue;
      
      let currentDate = new Date(searchStartDate);
      const currentDayIndex = currentDate.getDay();
      const daysUntilTarget = (targetDayIndex - currentDayIndex + 7) % 7;
      currentDate.setDate(currentDate.getDate() + daysUntilTarget);
      
      while (currentDate <= periodEnd && allDates.length < maxDates) {
        allDates.push({
          date: new Date(currentDate),
          dayOfWeek: dayNames[currentDate.getDay()],
          period: period
        });
        currentDate.setDate(currentDate.getDate() + 7);
      }
    }
    else if (period.frequency_type === 'X_DAYS') {
      const intervalDays = parseInt(period.frequency_value);
      let currentDate = new Date(searchStartDate);
      
      const daysSinceStart = Math.floor((currentDate.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24));
      const nextValidDay = Math.ceil(daysSinceStart / intervalDays) * intervalDays;
      
      currentDate = new Date(periodStart);
      currentDate.setDate(periodStart.getDate() + nextValidDay);
      
      while (currentDate <= periodEnd && allDates.length < maxDates) {
        if (currentDate >= searchStartDate) {
          allDates.push({
            date: new Date(currentDate),
            dayOfWeek: dayNames[currentDate.getDay()],
            period: period
          });
        }
        currentDate.setDate(currentDate.getDate() + intervalDays);
      }
    }
    else if (period.frequency_type === 'MONTHLY_SPECIFIC') {
      const monthlyDays = period.frequency_value.split(',').map(d => parseInt(d.trim()));
      let currentDate = new Date(searchStartDate);
      
      while (currentDate <= periodEnd && allDates.length < maxDates) {
        monthlyDays.forEach(day => {
          const testDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
          if (testDate.getMonth() === currentDate.getMonth() && 
              testDate >= searchStartDate && 
              testDate <= periodEnd) {
            allDates.push({
              date: new Date(testDate),
              dayOfWeek: dayNames[testDate.getDay()],
              period: period
            });
          }
        });
        
        currentDate.setMonth(currentDate.getMonth() + 1);
        currentDate.setDate(1);
      }
    }
  }
  
  // Sort dates and remove duplicates
  const uniqueDates = Array.from(
    new Map(allDates.map(item => [item.date.getTime(), item])).values()
  ).sort((a, b) => a.date.getTime() - b.date.getTime());
  
  return uniqueDates.slice(0, maxDates);
}

// Test function for a specific convict
function testConvict(convictId) {
  console.log(`\n=== HÜKÜMLü ${convictId} TEST ===`);
  
  // Get convict info
  const convict = db.prepare(`
    SELECT id, first_name, last_name, tc_no, file_number 
    FROM convicts 
    WHERE id = ?
  `).get(convictId);
  
  if (!convict) {
    console.log(`❌ Hükümlü ${convictId} bulunamadı`);
    return;
  }
  
  console.log(`👤 ${convict.first_name} ${convict.last_name} (Dosya: ${convict.file_number})`);
  
  // Get active signature periods
  const periods = db.prepare(`
    SELECT * FROM signature_periods 
    WHERE convict_id = ? AND is_active = 1
    ORDER BY start_date
  `).all(convictId);
  
  if (periods.length === 0) {
    console.log('❌ Aktif imza dönemi bulunamadı');
    return;
  }
  
  console.log(`📅 Aktif Dönem Sayısı: ${periods.length}`);
  periods.forEach((period, index) => {
    console.log(`   ${index + 1}. ${period.frequency_type} ${period.frequency_value} (${period.start_date} - ${period.end_date})`);
  });
  
  // Generate signature dates
  const generatedDates = generateSignatureDates(periods, 15);
  
  console.log(`🗓️  Üretilen İmza Tarihleri (${generatedDates.length} adet):`);
  generatedDates.forEach((item, index) => {
    const dateStr = item.date.toLocaleDateString('tr-TR');
    const dayStr = item.dayOfWeek;
    const periodStr = `${item.period.frequency_type} ${item.period.frequency_value}`;
    console.log(`   ${index + 1}. ${dateStr} (${dayStr}) - ${periodStr}`);
  });
  
  // Validate WEEKLY periods
  periods.filter(p => p.frequency_type === 'WEEKLY').forEach(period => {
    const targetDay = period.frequency_value.toUpperCase();
    const weeklyDates = generatedDates.filter(d => d.period.id === period.id);
    
    console.log(`\n✅ WEEKLY ${targetDay} Doğrulama:`);
    weeklyDates.forEach(item => {
      const isCorrectDay = item.dayOfWeek === targetDay;
      console.log(`   ${item.date.toLocaleDateString('tr-TR')} (${item.dayOfWeek}) - ${isCorrectDay ? '✅' : '❌'}`);
    });
  });
  
  // Validate MONTHLY_SPECIFIC periods
  periods.filter(p => p.frequency_type === 'MONTHLY_SPECIFIC').forEach(period => {
    const monthlyDays = period.frequency_value.split(',').map(d => parseInt(d.trim()));
    const monthlyDates = generatedDates.filter(d => d.period.id === period.id);
    
    console.log(`\n✅ MONTHLY_SPECIFIC ${period.frequency_value} Doğrulama:`);
    monthlyDates.forEach(item => {
      const dayOfMonth = item.date.getDate();
      const isCorrectDay = monthlyDays.includes(dayOfMonth);
      console.log(`   ${item.date.toLocaleDateString('tr-TR')} (${dayOfMonth}. gün) - ${isCorrectDay ? '✅' : '❌'}`);
    });
  });
  
  // Validate X_DAYS periods
  periods.filter(p => p.frequency_type === 'X_DAYS').forEach(period => {
    const intervalDays = parseInt(period.frequency_value);
    const xDaysDates = generatedDates.filter(d => d.period.id === period.id);
    
    console.log(`\n✅ X_DAYS ${intervalDays} Doğrulama:`);
    for (let i = 1; i < xDaysDates.length; i++) {
      const prevDate = xDaysDates[i-1].date;
      const currDate = xDaysDates[i].date;
      const daysDiff = Math.floor((currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));
      const isCorrectInterval = daysDiff === intervalDays;
      console.log(`   ${prevDate.toLocaleDateString('tr-TR')} → ${currDate.toLocaleDateString('tr-TR')} (${daysDiff} gün) - ${isCorrectInterval ? '✅' : '❌'}`);
    }
  });
}

// Main test execution
console.log('🚀 İmza Tarihi Üretim Doğrulama Testi');
console.log('=====================================');
console.log('📅 Test Tarihi:', new Date().toLocaleString('tr-TR'));

// Test all convicts with active periods
const convictsWithPeriods = db.prepare(`
  SELECT DISTINCT c.id, c.first_name, c.last_name, c.file_number,
         COUNT(sp.id) as period_count
  FROM convicts c
  INNER JOIN signature_periods sp ON c.id = sp.convict_id
  WHERE sp.is_active = 1
  GROUP BY c.id
  ORDER BY c.id
`).all();

console.log(`\n📊 Aktif İmza Dönemi Olan Hükümlüler: ${convictsWithPeriods.length}`);
convictsWithPeriods.forEach(c => {
  console.log(`   ${c.id}: ${c.first_name} ${c.last_name} (${c.period_count} dönem)`);
});

// Test each convict
convictsWithPeriods.forEach(convict => {
  testConvict(convict.id);
});

console.log('\n🎯 Test Tamamlandı!');
console.log('=====================================');

// Close database
db.close();
