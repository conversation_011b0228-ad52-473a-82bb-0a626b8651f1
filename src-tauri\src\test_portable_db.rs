// Test script to verify portable database path detection
use std::path::PathBuf;

// Copy the portable detection logic from database.rs
fn is_portable_mode() -> bool {
    if let Ok(exe_path) = std::env::current_exe() {
        if let Some(exe_name) = exe_path.file_name() {
            if let Some(name_str) = exe_name.to_str() {
                return name_str.contains("Portable") || name_str == "EITS-Portable.exe";
            }
        }
    }
    false
}

fn get_database_path() -> PathBuf {
    use std::env;

    // Check if we're running in portable mode
    if is_portable_mode() {
        println!("🔧 Running in portable mode - using executable directory for database");
        
        // Get the executable's directory
        if let Ok(exe_path) = env::current_exe() {
            if let Some(exe_dir) = exe_path.parent() {
                let mut path = exe_dir.to_path_buf();
                path.push("database.sqlite");
                println!("📁 Portable database path: {:?}", path);
                return path;
            }
        }
        
        // Fallback if we can't get exe path
        println!("⚠️ Could not determine executable directory, falling back to current directory");
        let mut path = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
        path.push("database.sqlite");
        return path;
    }

    println!("🔧 Running in normal mode - using Local AppData for database");

    // Try to get the local app data directory first (for production builds)
    if let Ok(local_app_data) = env::var("LOCALAPPDATA") {
        let mut path = PathBuf::from(local_app_data);
        path.push("com.eits.app");
        // Create directory if it doesn't exist
        if let Err(e) = std::fs::create_dir_all(&path) {
            eprintln!("Warning: Could not create local app data directory: {}", e);
        } else {
            path.push("database.sqlite");
            println!("📁 Local AppData database path: {:?}", path);
            return path;
        }
    }

    // Fallback to APPDATA if LOCALAPPDATA is not available
    if let Ok(app_data) = env::var("APPDATA") {
        let mut path = PathBuf::from(app_data);
        path.push("com.eits.app");
        // Create directory if it doesn't exist
        if let Err(e) = std::fs::create_dir_all(&path) {
            eprintln!("Warning: Could not create app data directory: {}", e);
        } else {
            path.push("database.sqlite");
            println!("📁 AppData database path: {:?}", path);
            return path;
        }
    }

    // Fallback to current directory approach (for development)
    println!("🔧 Fallback to development mode - using current directory");
    let mut path = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));

    // If we're in src-tauri directory, go up one level
    if path.file_name().and_then(|s| s.to_str()) == Some("src-tauri") {
        path.pop();
    }

    path.push("database.sqlite");
    println!("📁 Development database path: {:?}", path);
    path
}

fn main() {
    println!("🔍 Testing portable database path detection...");
    println!("");
    
    // Show current executable info
    if let Ok(exe_path) = std::env::current_exe() {
        println!("📍 Current executable: {:?}", exe_path);
        if let Some(exe_name) = exe_path.file_name() {
            println!("📝 Executable name: {:?}", exe_name);
        }
        if let Some(exe_dir) = exe_path.parent() {
            println!("📁 Executable directory: {:?}", exe_dir);
        }
    } else {
        println!("❌ Could not get current executable path");
    }
    
    println!("");
    
    // Test portable mode detection
    let is_portable = is_portable_mode();
    println!("🔧 Portable mode detected: {}", is_portable);
    
    println!("");
    
    // Test database path
    let db_path = get_database_path();
    println!("📊 Final database path: {:?}", db_path);
    
    // Check if database exists
    if db_path.exists() {
        println!("✅ Database file found!");
        
        // Check file size
        if let Ok(metadata) = std::fs::metadata(&db_path) {
            println!("📊 Database size: {} bytes", metadata.len());
        }
    } else {
        println!("❌ Database file not found at this location");
    }
    
    println!("");
    println!("🎯 Test completed!");
}
