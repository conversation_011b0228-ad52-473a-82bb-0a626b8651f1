import { use<PERSON>ara<PERSON> } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import DataTable from '@/components/common/DataTable';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ExemptionForm from './ExemptionForm';
import { useToast } from '@/hooks/use-toast';
import { getConvicts, getConvictExemptions, createExemption, updateExemption, deleteExemption } from '@/lib/tauri-api';
import { formatDate } from '@/lib/utils';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import type { Exemption, InsertExemption } from '@shared/schema';

const exemptionTypeLabels = {
  LEAVE: 'İzin',
  MEDICAL_REPORT: 'Doktor Raporu'
};

const exemptionTypeColors = {
  LEAVE: 'bg-blue-100 text-blue-800 border-blue-200',
  MEDICAL_REPORT: 'bg-green-100 text-green-800 border-green-200'
};

export default function ManageExemptionsPage() {
  const { id } = useParams();
  const convictId = Number(id);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingExemption, setEditingExemption] = useState<Exemption | null>(null);

  // Get convict data
  const { data: convicts, isLoading: convictLoading } = useQuery({
    queryKey: ['convicts'],
    queryFn: getConvicts,
  });

  const convict = convicts?.find(c => c.id === convictId);

  // Get exemptions for this convict
  const { data: exemptions, isLoading: exemptionsLoading } = useQuery({
    queryKey: ['exemptions', convictId],
    queryFn: () => getConvictExemptions(convictId),
    enabled: !!convictId,
  });

  // Create exemption mutation
  const createExemptionMutation = useMutation({
    mutationFn: async (exemptionData: InsertExemption) => {
      return await createExemption({ ...exemptionData, convict_id: convictId });
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Muafiyet başarıyla eklendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['exemptions', convictId] });
      handleDialogClose();
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Muafiyet eklenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  // Update exemption mutation
  const updateExemptionMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: InsertExemption }) => {
      return await updateExemption(id, { ...data, convict_id: convictId });
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Muafiyet başarıyla güncellendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['exemptions', convictId] });
      handleDialogClose();
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Muafiyet güncellenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  // Delete exemption mutation
  const deleteExemptionMutation = useMutation({
    mutationFn: deleteExemption,
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Muafiyet başarıyla silindi.',
      });
      queryClient.invalidateQueries({ queryKey: ['exemptions', convictId] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Muafiyet silinirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const handleCreateExemption = (exemptionData: InsertExemption) => {
    createExemptionMutation.mutate(exemptionData);
  };

  const handleUpdateExemption = (exemptionData: InsertExemption) => {
    if (editingExemption) {
      updateExemptionMutation.mutate({
        id: editingExemption.id,
        data: exemptionData,
      });
    }
  };

  const handleDeleteExemption = (exemptionId: number) => {
    deleteExemptionMutation.mutate(exemptionId);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingExemption(null);
  };

  const isCurrentlyActive = (exemption: Exemption) => {
    const now = new Date();
    const startDate = new Date(exemption.start_date);
    const endDate = new Date(exemption.end_date);
    return exemption.is_active && now >= startDate && now <= endDate;
  };

  if (convictLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 font-medium">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!convict) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">!</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Hükümlü Bulunamadı</h2>
          <p className="text-gray-600">Aradığınız hükümlü kaydı bulunamadı.</p>
        </div>
      </div>
    );
  }

  const columns = [
    {
      key: 'exemptionType',
      label: 'Muafiyet Türü',
      render: (exemption: Exemption) => (
        <Badge 
          className={`text-xs px-2 py-1 rounded ${exemptionTypeColors[exemption.exemption_type as keyof typeof exemptionTypeColors]}`}
        >
          {exemptionTypeLabels[exemption.exemption_type as keyof typeof exemptionTypeLabels]}
        </Badge>
      ),
    },
    {
      key: 'startDate',
      label: 'Başlangıç Tarihi',
      render: (exemption: Exemption) => (
        <div className="font-medium text-gray-900">
          {formatDate(exemption.start_date)}
        </div>
      ),
    },
    {
      key: 'endDate',
      label: 'Bitiş Tarihi',
      render: (exemption: Exemption) => (
        <div className="font-medium text-gray-900">
          {formatDate(exemption.end_date)}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Durum',
      render: (exemption: Exemption) => {
        const isActive = isCurrentlyActive(exemption);
        return (
          <Badge 
            variant={isActive ? 'default' : 'secondary'}
            className={`text-xs px-2 py-1 rounded ${
              isActive
                ? 'bg-green-100 text-green-800 border-green-200' 
                : 'bg-gray-100 text-gray-600 border-gray-200'
            }`}
          >
            {isActive ? 'Aktif' : 'Pasif'}
          </Badge>
        );
      },
    },
    {
      key: 'description',
      label: 'Açıklama',
      render: (exemption: Exemption) => (
        <div className="text-sm text-gray-600 max-w-xs truncate">
          {exemption.description || '-'}
        </div>
      ),
    },
  ];

  // Table actions
  const actions = (exemption: Exemption) => (
    <div className="flex items-center justify-end space-x-1">
      <Button 
        variant="windows" 
        size="sm"
        className="h-7 w-7 p-0"
        title="Düzenle"
        onClick={() => {
          setEditingExemption(exemption);
          setIsDialogOpen(true);
        }}
      >
        <PencilIcon className="w-3 h-3" />
      </Button>
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button 
            variant="windows" 
            size="sm"
            className="h-7 w-7 p-0 text-red-600 hover:text-red-700"
            title="Sil"
          >
            <TrashIcon className="w-3 h-3" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Muafiyeti Sil</AlertDialogTitle>
            <AlertDialogDescription>
              Bu muafiyeti silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleDeleteExemption(exemption.id)}
              className="bg-red-600 hover:bg-red-700"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );

  return (
    <div className="windows-content">
      {/* Header */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">Muafiyet Yönetimi</h1>
            <p className="text-sm text-gray-600 mt-1">
              {convict.first_name} {convict.last_name} - {convict.tc_no}
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="windows-button-primary">
                <PlusIcon className="w-4 h-4 mr-1" />
                Yeni Muafiyet
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingExemption ? 'Muafiyeti Düzenle' : 'Yeni Muafiyet Ekle'}
                </DialogTitle>
              </DialogHeader>
              <ExemptionForm
                initialData={editingExemption ? {
                  exemptionType: editingExemption.exemption_type,
                  startDate: editingExemption.start_date,
                  endDate: editingExemption.end_date,
                  description: editingExemption.description || '',
                  documentPath: editingExemption.document_path || '',
                  isActive: editingExemption.is_active,
                } : undefined}
                onSubmit={editingExemption ? handleUpdateExemption : handleCreateExemption}
                onCancel={handleDialogClose}
                isLoading={createExemptionMutation.isPending || updateExemptionMutation.isPending}
                submitLabel={editingExemption ? 'Güncelle' : 'Ekle'}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <Card className="backdrop-blur-sm bg-white/70 border-white/20 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl">
            <CardTitle className="text-gray-800 flex items-center">
              <CalendarIcon className="w-5 h-5 mr-2 text-blue-600" />
              Muafiyetler ({exemptions?.length || 0})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {exemptionsLoading ? (
              <div className="flex items-center justify-center py-8">
                <LoadingSpinner size="lg" />
                <span className="ml-3 text-gray-600">Muafiyetler yükleniyor...</span>
              </div>
            ) : exemptions && exemptions.length > 0 ? (
              <DataTable
                data={exemptions}
                columns={columns}
                actions={actions}
                emptyMessage="Henüz muafiyet kaydı bulunmuyor."
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <CalendarIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium mb-2">Henüz muafiyet kaydı yok</p>
                <p className="text-sm">Bu hükümlü için henüz bir muafiyet kaydı oluşturulmamış.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
