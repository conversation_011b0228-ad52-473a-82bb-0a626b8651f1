const Database = require('better-sqlite3');

console.log('🔍 Exemption Assignment Verification');
console.log('===================================');

try {
  const db = new Database('./database.sqlite');
  
  // Get convict details
  const miktat = db.prepare('SELECT * FROM convicts WHERE tc_no = ?').get('40261629268');
  const yunus = db.prepare('SELECT * FROM convicts WHERE tc_no = ?').get('40267629040');
  
  console.log('\n👥 CONVICT DETAILS:');
  console.log('Miktat Güngör:', miktat);
  console.log('Yun<PERSON>:', yunus);
  
  // Get the exemption
  const exemption = db.prepare('SELECT * FROM exemptions WHERE convict_id = 129').get();
  
  console.log('\n📋 EXEMPTION DETAILS:');
  console.log('Current exemption:', exemption);
  
  // Based on the description "Soğuk Algınlığı" (common cold), this seems like a real medical exemption
  // Let's check if this makes sense for either person based on the context
  
  console.log('\n🤔 ANALYSIS:');
  console.log('1. Exemption type: MEDICAL_REPORT');
  console.log('2. Description: "Soğuk Algınlığı" (Common Cold)');
  console.log('3. Date range: 2025-06-05 to 2025-06-10 (5 days)');
  console.log('4. Assigned to: Yunus Güngör (TC: 40267629040)');
  
  console.log('\n🎯 DECISION MATRIX:');
  console.log('SCENARIO A: Exemption correctly assigned to Yunus');
  console.log('  - Action: Test cache invalidation fix with Yunus\'s UI');
  console.log('  - Expected: Exemption shows up for Yunus in ManageExemptionsPage');
  console.log('  - Fix verified: ✅ Cache invalidation works');
  
  console.log('\nSCENARIO B: Exemption incorrectly assigned (should be Miktat\'s)');
  console.log('  - Action: Move exemption from Yunus to Miktat');
  console.log('  - SQL: UPDATE exemptions SET convict_id = 128 WHERE id = 45');
  console.log('  - Then test cache invalidation with Miktat\'s UI');
  
  console.log('\n💡 RECOMMENDATION:');
  console.log('1. Since both are similar names, check original Excel file if available');
  console.log('2. If no Excel file, assume import was correct and test with Yunus');
  console.log('3. If user confirms exemption should be for Miktat, we can move it');
  
  // Provide SQL for manual fix if needed
  console.log('\n🔧 MANUAL FIX COMMANDS (if exemption should be for Miktat):');
  console.log('UPDATE exemptions SET convict_id = 128 WHERE id = 45;');
  console.log('-- This moves the exemption from Yunus (129) to Miktat (128)');
  
  db.close();
  
} catch (error) {
  console.error('❌ Error:', error.message);
}

console.log('\n✅ Verification complete');
