// Utility functions for generating signature dates based on convict periods
import type { SignaturePeriod } from '../shared/schema';

export interface GeneratedSignatureDate {
  date: string; // DD.MM.YYYY format
  period: SignaturePeriod;
  dayOfWeek: string;
}

// Convert day names to Turkish
const dayNamesTurkish: Record<string, string> = {
  'SUNDAY': 'Pazar',
  'MONDAY': 'Pazartesi', 
  'TUESDAY': 'Salı',
  'WEDNESDAY': 'Çarşamba',
  'THURSDAY': 'Perşembe',
  'FRIDAY': 'Cuma',
  'SATURDAY': '<PERSON>uma<PERSON><PERSON>'
};

// Get Turkish day name for a date
const getTurkishDayName = (date: Date): string => {
  const dayNames = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
  const dayName = dayNames[date.getDay()];
  return dayNamesTurkish[dayName] || dayName;
};

// Generate signature dates for multiple periods
export const generateSignatureDates = (
  periods: SignaturePeriod[], 
  maxDates: number = 24,
  startFromToday: boolean = true
): GeneratedSignatureDate[] => {
  const generatedDates: GeneratedSignatureDate[] = [];
  const today = new Date();
  
  if (!periods || periods.length === 0) {
    // Fallback: generate next maxDates days if no active periods
    for (let i = 0; i < maxDates; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      generatedDates.push({
        date: date.toLocaleDateString('tr-TR'),
        period: {} as SignaturePeriod, // Empty period for fallback
        dayOfWeek: getTurkishDayName(date)
      });
    }
    return generatedDates;
  }

  // Process each period with optimized date generation
  periods.forEach((period) => {
    const startDate = new Date(period.start_date);
    const endDate = new Date(period.end_date);
    const searchStartDate = new Date(startFromToday ? Math.max(startDate.getTime(), today.getTime()) : startDate.getTime());

    if (period.frequency_type === 'WEEKLY') {
      // Optimized weekly date generation
      const weeklyDays = (period.frequency_value || '').split(',').map(day => day.trim());
      const dayNames = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
      
      weeklyDays.forEach(targetDay => {
        const targetDayIndex = dayNames.indexOf(targetDay);
        if (targetDayIndex === -1) return;
        
        // Find the first occurrence of target day from search start date
        let currentDate = new Date(searchStartDate);
        const currentDayIndex = currentDate.getDay();
        const daysUntilTarget = (targetDayIndex - currentDayIndex + 7) % 7;
        currentDate.setDate(currentDate.getDate() + daysUntilTarget);
        
        // Generate weekly occurrences
        while (currentDate <= endDate && generatedDates.length < maxDates * 2) {
          const dateStr = currentDate.toLocaleDateString('tr-TR');
          const existingDate = generatedDates.find(d => d.date === dateStr);
          
          if (!existingDate) {
            generatedDates.push({
              date: dateStr,
              period: period,
              dayOfWeek: getTurkishDayName(currentDate)
            });
          }
          
          // Move to next week
          currentDate.setDate(currentDate.getDate() + 7);
        }
      });
      
    } else if (period.frequency_type === 'X_DAYS') {
      // Optimized X_DAYS date generation
      const intervalDays = parseInt(period.frequency_value || '1');
      let currentDate = new Date(searchStartDate);
      
      // Find the first valid date from period start
      const periodStart = new Date(period.start_date);
      let daysSinceStart = Math.floor((currentDate.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24));
      
      // Adjust to next valid interval if needed
      if (daysSinceStart < 0) {
        currentDate = new Date(periodStart);
        daysSinceStart = 0;
      } else if (daysSinceStart % intervalDays !== 0) {
        const nextValidDay = Math.ceil(daysSinceStart / intervalDays) * intervalDays;
        currentDate = new Date(periodStart);
        currentDate.setDate(periodStart.getDate() + nextValidDay);
      }
      
      // Generate dates at intervals
      while (currentDate <= endDate && generatedDates.length < maxDates * 2) {
        const dateStr = currentDate.toLocaleDateString('tr-TR');
        const existingDate = generatedDates.find(d => d.date === dateStr);
        
        if (!existingDate) {
          generatedDates.push({
            date: dateStr,
            period: period,
            dayOfWeek: getTurkishDayName(currentDate)
          });
        }
        
        // Move to next interval
        currentDate.setDate(currentDate.getDate() + intervalDays);
      }
      
    } else if (period.frequency_type === 'MONTHLY_SPECIFIC') {
      // Optimized monthly specific date generation
      const monthlyDays = (period.frequency_value || '').split(',')
        .map(day => parseInt(day.trim()))
        .filter(day => !isNaN(day) && day >= 1 && day <= 31);
      
      let currentDate = new Date(searchStartDate);
      currentDate.setDate(1); // Start from first day of month
      
      while (currentDate <= endDate && generatedDates.length < maxDates * 2) {
        monthlyDays.forEach(day => {
          const testDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
          
          // Check if this day exists in this month and is within our range
          if (testDate.getMonth() === currentDate.getMonth() && 
              testDate >= searchStartDate && 
              testDate <= endDate) {
            
            const dateStr = testDate.toLocaleDateString('tr-TR');
            const existingDate = generatedDates.find(d => d.date === dateStr);
            
            if (!existingDate) {
              generatedDates.push({
                date: dateStr,
                period: period,
                dayOfWeek: getTurkishDayName(testDate)
              });
            }
          }
        });
        
        // Move to next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }
  });

  // Sort by date and return first maxDates
  const sortedDates = generatedDates.sort((a, b) => {
    const dateA = new Date(a.date.split('.').reverse().join('-'));
    const dateB = new Date(b.date.split('.').reverse().join('-'));
    return dateA.getTime() - dateB.getTime();
  });

  return sortedDates.slice(0, maxDates);
};

// Format period display for the signature form
export const formatPeriodDisplay = (period: SignaturePeriod) => {
  let frequency = '';
  let days = '';
  let time = 'Saat 09:00 - 17:00'; // Default time range

  // Use actual time range from database if available
  if (period.time_start && period.time_end) {
    time = `Saat ${period.time_start} - ${period.time_end}`;
  }

  switch (period.frequency_type) {
    case 'WEEKLY': {
      const weeklyDays = (period.frequency_value || '').split(',').map(day => day.trim());
      const translatedDays = weeklyDays.map(day => dayNamesTurkish[day] || day).filter(Boolean);
      frequency = `Haftada ${translatedDays.length} Gün`;
      days = translatedDays.join(', ');
      break;
    }
    case 'X_DAYS': {
      const xDaysNum = parseInt(period.frequency_value || '1');
      frequency = `${xDaysNum} Günde Bir`;
      days = 'Belirli aralıklarla';
      break;
    }
    case 'MONTHLY_SPECIFIC': {
      const monthlyDays = (period.frequency_value || '').split(',').map(day => day.trim()).filter(Boolean);
      frequency = `Ayda ${monthlyDays.length} Gün`;
      days = monthlyDays.join(', ') + '. günlerde';
      break;
    }
  }

  return { frequency, days, time };
};

// Time validation functions for signature periods
export interface TimeValidationResult {
  isValid: boolean;
  errors: string[];
}

// Check if current time is within allowed time range for a signature period
export const validateSignatureTime = (
  period: SignaturePeriod,
  currentTime?: string, // HH:MM format, defaults to current time
  currentDate?: string  // YYYY-MM-DD format, defaults to current date
): TimeValidationResult => {
  const errors: string[] = [];
  
  // Get current time if not provided
  const now = new Date();
  const timeToCheck = currentTime || now.toTimeString().slice(0, 5); // HH:MM
  const dateToCheck = currentDate || now.toISOString().slice(0, 10); // YYYY-MM-DD
  
  // Check if period has time restrictions
  if (period.time_start && period.time_end) {
    if (timeToCheck < period.time_start || timeToCheck > period.time_end) {
      errors.push(
        `İmza sadece ${period.time_start} - ${period.time_end} saatleri arasında atılabilir`
      );
    }
  }
  
  // Check if period has day restrictions
  if (period.allowed_days) {
    try {
      const allowedDays: string[] = JSON.parse(period.allowed_days);
      if (allowedDays.length > 0) {
        const date = new Date(dateToCheck);
        const dayNames = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
        const currentDayName = dayNames[date.getDay()];
        
        if (!allowedDays.includes(currentDayName)) {
          const turkishDayNames = allowedDays.map(day => dayNamesTurkish[day] || day);
          errors.push(
            `İmza sadece şu günlerde atılabilir: ${turkishDayNames.join(', ')}`
          );
        }
      }
    } catch (e) {
      console.warn('Failed to parse allowed_days JSON:', period.allowed_days);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Check if multiple periods have time restrictions conflicts
export const validatePeriodsTimeConflicts = (periods: SignaturePeriod[]): TimeValidationResult => {
  const errors: string[] = [];
  const activePeriods = periods.filter(p => p.is_active);
  
  // Check for overlapping time restrictions on same days
  for (let i = 0; i < activePeriods.length; i++) {
    for (let j = i + 1; j < activePeriods.length; j++) {
      const period1 = activePeriods[i];
      const period2 = activePeriods[j];
      
      // Check if periods overlap in dates
      const start1 = new Date(period1.start_date);
      const end1 = new Date(period1.end_date);
      const start2 = new Date(period2.start_date);
      const end2 = new Date(period2.end_date);
      
      const datesOverlap = start1 <= end2 && start2 <= end1;
      
      if (datesOverlap) {
        // Check if they have conflicting time restrictions
        const hasTimeRestrictions1 = period1.time_start && period1.time_end;
        const hasTimeRestrictions2 = period2.time_start && period2.time_end;
        
        if (hasTimeRestrictions1 && hasTimeRestrictions2) {
          // Check if time ranges overlap
          const timeOverlap = period1.time_start! <= period2.time_end! && period2.time_start! <= period1.time_end!;
          
          if (timeOverlap) {
            errors.push(
              `${start1.toLocaleDateString('tr-TR')} - ${end1.toLocaleDateString('tr-TR')} ve ` +
              `${start2.toLocaleDateString('tr-TR')} - ${end2.toLocaleDateString('tr-TR')} ` +
              `periyotlarının zaman kısıtlamaları çakışıyor`
            );
          }
        }
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Get readable time restriction text for display
export const getTimeRestrictionText = (period: SignaturePeriod): string => {
  const restrictions: string[] = [];
  
  if (period.time_start && period.time_end) {
    restrictions.push(`Saat ${period.time_start} - ${period.time_end}`);
  }
  
  if (period.allowed_days) {
    try {
      const allowedDays: string[] = JSON.parse(period.allowed_days);
      if (allowedDays.length > 0) {
        const turkishDayNames = allowedDays.map(day => dayNamesTurkish[day] || day);
        restrictions.push(`Günler: ${turkishDayNames.join(', ')}`);
      }
    } catch (e) {
      console.warn('Failed to parse allowed_days JSON:', period.allowed_days);
    }
  }
  
  return restrictions.length > 0 ? restrictions.join(' | ') : 'Kısıtlama yok';
};

// Group signature periods with same date range, type, and time for display
export const groupSignaturePeriods = (periods: SignaturePeriod[]) => {
  const groups: Map<string, SignaturePeriod[]> = new Map();
  
  // Group periods by date range, type, and time
  periods.forEach(period => {
    const key = `${period.start_date}_${period.end_date}_${period.frequency_type}_${period.time_start || ''}_${period.time_end || ''}`;
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key)!.push(period);
  });
  
  // Convert groups to consolidated periods
  const groupedPeriods = Array.from(groups.values()).map(groupPeriods => {
    // Use the first period as base
    const basePeriod = groupPeriods[0];
    
    // Merge frequency values based on type
    let mergedFrequencyValue = '';
    if (basePeriod.frequency_type === 'WEEKLY') {
      // Collect all weekly days and deduplicate
      const allDays = new Set<string>();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      mergedFrequencyValue = Array.from(allDays).join(',');
    } else if (basePeriod.frequency_type === 'MONTHLY_SPECIFIC') {
      // Collect all monthly days and deduplicate
      const allDays = new Set<string>();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      // Sort numerically for monthly days
      const sortedDays = Array.from(allDays).sort((a, b) => parseInt(a) - parseInt(b));
      mergedFrequencyValue = sortedDays.join(',');
    } else {
      // For X_DAYS type, use the first period's value
      mergedFrequencyValue = basePeriod.frequency_value;
    }
    
    // Return a merged period
    return {
      ...basePeriod,
      frequency_value: mergedFrequencyValue
    };
  });
  
  return groupedPeriods;
};
