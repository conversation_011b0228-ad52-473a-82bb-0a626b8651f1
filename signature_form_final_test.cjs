#!/usr/bin/env node

/**
 * Final Signature Form Test Script
 * Tests the complete signature form functionality after all fixes
 */

const Database = require('better-sqlite3');
const path = require('path');
const { execSync } = require('child_process');

const DB_PATH = path.join(__dirname, 'database.sqlite');

console.log('🧪 EITS Signature Form - Final Integration Test');
console.log('=' .repeat(50));

try {
  // Test 1: Database Connection and Convict Data
  console.log('\n📊 Test 1: Database Connection and Convict Data');
  const db = new Database(DB_PATH);
  
  const convict = db.prepare(`
    SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
           phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at
    FROM convicts WHERE id = 101
  `).get();
  
  console.log('✅ Database connection successful');
  console.log(`✅ Convict data loaded: ${convict.first_name} ${convict.last_name}`);
  console.log(`✅ Phone number: ${convict.phone_number || 'N/A'}`);
  console.log(`✅ Relative phone: ${convict.relative_phone_number || 'N/A'}`);
  console.log(`✅ Address: ${convict.address || 'N/A'}`);
  console.log(`✅ File number: ${convict.file_number || 'N/A'}`);
  
  // Test 2: Signature Periods
  console.log('\n📅 Test 2: Signature Periods');
  const periods = db.prepare(`
    SELECT id, frequency_type, frequency_value, time_start, time_end, allowed_days, start_date, end_date, is_active
    FROM signature_periods WHERE convict_id = 101 AND is_active = 1
  `).all();
  
  console.log(`✅ Found ${periods.length} active signature periods`);
  periods.forEach((period, index) => {
    console.log(`   Period ${index + 1}: ${period.frequency_type} - ${period.frequency_value}`);
    console.log(`   Time: ${period.time_start || 'Not set'} - ${period.time_end || 'Not set'}`);
    console.log(`   Date range: ${period.start_date} to ${period.end_date}`);
  });
  
  // Test 3: Date Generation Logic (Simplified Test)
  console.log('\n🗓️  Test 3: Date Generation Logic');
  
  const currentDate = new Date();
  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + 3); // 3 months ahead
  
  let totalExpectedDates = 0;
  
  periods.forEach(period => {
    const start = new Date(period.start_date);
    const end = new Date(period.end_date);
    const actualEnd = end < endDate ? end : endDate;
    
    if (period.frequency_type === 'WEEKLY') {
      const weeklyDates = Math.floor((actualEnd - Math.max(start, currentDate)) / (7 * 24 * 60 * 60 * 1000));
      totalExpectedDates += Math.max(0, weeklyDates);
    } else if (period.frequency_type === 'MONTHLY_SPECIFIC') {
      const monthlyDates = Math.floor((actualEnd.getFullYear() - start.getFullYear()) * 12 + (actualEnd.getMonth() - start.getMonth() + 1));
      const daysInMonth = period.frequency_value.split(',').length;
      totalExpectedDates += Math.max(0, monthlyDates * daysInMonth);
    }
  });
  
  console.log(`✅ Estimated signature dates to generate: ~${totalExpectedDates}`);
  
  // Test 4: Data Completeness Check
  console.log('\n✅ Test 4: Data Completeness Check');
  
  const requiredFields = [
    { field: 'tc_no', value: convict.tc_no, label: 'TC Kimlik No' },
    { field: 'first_name', value: convict.first_name, label: 'Ad' },
    { field: 'last_name', value: convict.last_name, label: 'Soyad' },
    { field: 'file_number', value: convict.file_number, label: 'Dosya No' }
  ];
  
  let allFieldsComplete = true;
  requiredFields.forEach(field => {
    if (field.value && field.value.trim() !== '') {
      console.log(`   ✅ ${field.label}: ${field.value}`);
    } else {
      console.log(`   ❌ ${field.label}: Missing`);
      allFieldsComplete = false;
    }
  });
  
  const contactFields = [
    { field: 'phone_number', value: convict.phone_number, label: 'Telefon' },
    { field: 'relative_phone_number', value: convict.relative_phone_number, label: 'Yakın Telefon' },
    { field: 'address', value: convict.address, label: 'Adres' }
  ];
  
  contactFields.forEach(field => {
    if (field.value && field.value.trim() !== '') {
      console.log(`   ✅ ${field.label}: ${field.value}`);
    } else {
      console.log(`   ⚠️  ${field.label}: Boş (isteğe bağlı)`);
    }
  });
  
  // Final Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📋 FINAL TEST SUMMARY');
  console.log('=' .repeat(50));
  
  if (allFieldsComplete && periods.length > 0) {
    console.log('🎉 SUCCESS: Signature form is ready!');
    console.log('✅ All required fields are populated');
    console.log('✅ Signature periods are configured');
    console.log('✅ Date generation logic should work');
    console.log('\n🎯 You can now test the signature form at:');
    console.log('   http://localhost:1420/convicts/101/signature-form');
  } else {
    console.log('⚠️  WARNING: Some issues detected');
    if (!allFieldsComplete) {
      console.log('❌ Missing required fields');
    }
    if (periods.length === 0) {
      console.log('❌ No active signature periods');
    }
  }
  
  db.close();
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  process.exit(1);
}
