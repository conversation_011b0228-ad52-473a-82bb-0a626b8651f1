#!/usr/bin/env node
/**
 * Test script for file number search functionality
 * This script tests the new file number search feature in the convict list
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 File Number Search Implementation Test');
console.log('=========================================\n');

// Check if the backend files contain the new search function
const databaseRsPath = path.join(__dirname, 'src-tauri', 'src', 'database.rs');
const commandsRsPath = path.join(__dirname, 'src-tauri', 'src', 'commands.rs');
const libRsPath = path.join(__dirname, 'src-tauri', 'src', 'lib.rs');
const tauriApiPath = path.join(__dirname, 'src', 'lib', 'tauri-api.ts');
const convictListPath = path.join(__dirname, 'src', 'features', 'convicts', 'ConvictListPage.tsx');

console.log('✅ Checking backend implementation...');

// Check database.rs
if (fs.existsSync(databaseRsPath)) {
    const databaseContent = fs.readFileSync(databaseRsPath, 'utf8');
    if (databaseContent.includes('search_convicts_by_file_number')) {
        console.log('  ✅ Database function search_convicts_by_file_number found');
    } else {
        console.log('  ❌ Database function search_convicts_by_file_number NOT found');
    }
} else {
    console.log('  ❌ database.rs file not found');
}

// Check commands.rs
if (fs.existsSync(commandsRsPath)) {
    const commandsContent = fs.readFileSync(commandsRsPath, 'utf8');
    if (commandsContent.includes('search_convicts_by_file_number')) {
        console.log('  ✅ Tauri command search_convicts_by_file_number found');
    } else {
        console.log('  ❌ Tauri command search_convicts_by_file_number NOT found');
    }
} else {
    console.log('  ❌ commands.rs file not found');
}

// Check lib.rs for command registration
if (fs.existsSync(libRsPath)) {
    const libContent = fs.readFileSync(libRsPath, 'utf8');
    if (libContent.includes('search_convicts_by_file_number')) {
        console.log('  ✅ Command registered in lib.rs');
    } else {
        console.log('  ❌ Command NOT registered in lib.rs');
    }
} else {
    console.log('  ❌ lib.rs file not found');
}

console.log('\n✅ Checking frontend implementation...');

// Check tauri-api.ts
if (fs.existsSync(tauriApiPath)) {
    const apiContent = fs.readFileSync(tauriApiPath, 'utf8');
    if (apiContent.includes('searchConvictsByFileNumber')) {
        console.log('  ✅ Frontend API function searchConvictsByFileNumber found');
    } else {
        console.log('  ❌ Frontend API function searchConvictsByFileNumber NOT found');
    }
} else {
    console.log('  ❌ tauri-api.ts file not found');
}

// Check ConvictListPage.tsx
if (fs.existsSync(convictListPath)) {
    const pageContent = fs.readFileSync(convictListPath, 'utf8');
    
    if (pageContent.includes("'file_number'")) {
        console.log('  ✅ file_number search type added to ConvictListPage');
    } else {
        console.log('  ❌ file_number search type NOT found in ConvictListPage');
    }
    
    if (pageContent.includes('searchConvictsByFileNumber')) {
        console.log('  ✅ searchConvictsByFileNumber import found');
    } else {
        console.log('  ❌ searchConvictsByFileNumber import NOT found');
    }
    
    if (pageContent.includes('Dosya No')) {
        console.log('  ✅ File number option added to search dropdown');
    } else {
        console.log('  ❌ File number option NOT found in search dropdown');
    }
    
    if (pageContent.includes('fileNumberSearchResults')) {
        console.log('  ✅ File number search query implemented');
    } else {
        console.log('  ❌ File number search query NOT implemented');
    }
} else {
    console.log('  ❌ ConvictListPage.tsx file not found');
}

console.log('\n🔍 Summary:');
console.log('The file number search functionality has been implemented with:');
console.log('- Backend database function for searching by file number');
console.log('- Tauri command to expose the search function');
console.log('- Frontend API function to call the backend');
console.log('- Updated UI with file number as a search option');
console.log('- Proper integration with existing search and filter logic');
console.log('\n✅ Implementation appears to be complete!');
console.log('\nTo test manually:');
console.log('1. Start the application with: pnpm tauri dev');
console.log('2. Navigate to the Convict List page');
console.log('3. Select "Dosya No" from the search type dropdown');
console.log('4. Enter a file number to search');
console.log('5. Verify that results are filtered correctly');
