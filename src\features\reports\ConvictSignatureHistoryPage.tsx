import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import DataTable from '@/components/common/DataTable';
import { formatDate, formatDateTime } from '@/lib/utils';
import { 
  searchConvictByTcNo, 
  searchConvictsByName, 
  getConvictSignatures 
} from '@/lib/tauri-api';
import {
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import type { Convict, Signature } from '@shared/schema';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function ConvictSignatureHistoryPage() {
  const [selectedConvictId, setSelectedConvictId] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [hasSearched, setHasSearched] = useState(false);

  // Search for convicts
  const { data: searchResults } = useQuery({
    queryKey: ['search-convicts', searchQuery],
    queryFn: async () => {
      if (!searchQuery.trim()) return [];
      
      let convicts: Convict[] = [];
      
      if (/^\d{11}$/.test(searchQuery.trim())) {
        // Search by TC No
        const convict = await searchConvictByTcNo(searchQuery.trim());
        if (convict) convicts = [convict];
      } else {
        // Search by name
        const nameParts = searchQuery.trim().split(' ');
        if (nameParts.length >= 2) {
          const firstName = nameParts[0];
          const lastName = nameParts.slice(1).join(' ');
          convicts = await searchConvictsByName(firstName, lastName);
        } else {
          convicts = await searchConvictsByName(searchQuery.trim());
        }
      }
      
      return convicts;
    },
    enabled: !!searchQuery.trim(),
  });

  // Get signature history for selected convict
  const { data: historyData, isLoading: isLoadingHistory, refetch } = useQuery<Signature[]>({
    queryKey: ['convict-signatures', selectedConvictId],
    queryFn: async () => {
      if (!selectedConvictId) return [];
      return await getConvictSignatures(parseInt(selectedConvictId));
    },
    enabled: !!selectedConvictId && hasSearched,
  });

  // Get selected convict details
  const selectedConvict = searchResults?.find(c => c.id.toString() === selectedConvictId);

  const handleGenerateReport = () => {
    if (!selectedConvictId) {
      alert('Lütfen bir hükümlü seçin');
      return;
    }
    setHasSearched(true);
    refetch();
  };

  const handleExportReport = () => {
    // Export functionality would be implemented here
    console.log('Rapor dışa aktarılıyor...');
  };  const columns = [
    {
      key: 'signatureDate',
      label: 'İmza Tarihi',
      render: (signature: Signature) => (
        <div className="font-medium text-gray-900">
          {formatDate(signature.signature_date)}
        </div>
      ),
    },
    {
      key: 'signatureTime',
      label: 'İmza Saati',
      render: (signature: Signature) => (
        <div className="text-sm">
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-mono">
            {signature.signature_time}
          </span>
        </div>
      ),
    },
    {
      key: 'recordedBy',
      label: 'Kaydeden',
      render: (signature: Signature) => (
        <div className="text-gray-700">
          {signature.recorded_by || 'Sistem'}
        </div>
      ),
    },
    {
      key: 'createdAt',
      label: 'Kayıt Tarihi',
      render: (signature: Signature) => (
        <div className="text-sm text-gray-600">
          {signature.created_at ? formatDateTime(signature.created_at) : '-'}
        </div>
      ),
    },
  ];

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">Hükümlü İmza Geçmişi</h1>
            <span className="text-gray-600 text-xs ml-2">
              - Seçilen hükümlünün detaylı imza kayıtlarını görüntüle
            </span>
          </div>
          {selectedConvict && (
            <div className="text-right">
              <span className="text-xs text-gray-600">Seçili Hükümlü: </span>
              <span className="text-xs font-medium">{selectedConvict.first_name} {selectedConvict.last_name}</span>
            </div>
          )}
        </div>
      </div>

      <div className="p-3">
        {/* Convict Selection */}
        <div className="windows-card mb-3">
          <div className="windows-section-title">Hükümlü Seçimi</div>
          <div className="p-3 space-y-3">
            {/* Search Input */}
            <div className="space-y-2">
              <Label htmlFor="convictSearch" className="text-gray-700 font-medium">
                Hükümlü Ara (TC No veya Ad Soyad)
              </Label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <Input
                  id="convictSearch"
                  placeholder="TC Kimlik No veya Ad Soyad ile ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 modern-input"
                />
              </div>
            </div>

            {/* Convict Selection */}
            {searchResults && searchResults.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="convictSelect" className="text-gray-700 font-medium">Hükümlü Seç</Label>
                <Select
                  value={selectedConvictId}
                  onValueChange={setSelectedConvictId}
                >
                  <SelectTrigger className="modern-input">
                    <SelectValue placeholder="Hükümlü seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {searchResults.map((convict: Convict) => (
                      <SelectItem key={convict.id} value={convict.id.toString()}>
                        <div className="flex items-center space-x-3">
                          <span className="font-mono text-sm">{convict.tc_no}</span>
                          <span>-</span>
                          <span className="font-medium">{convict.first_name} {convict.last_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleGenerateReport}
                disabled={!selectedConvictId || isLoadingHistory}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
                {isLoadingHistory ? 'Getiriliyor...' : 'Geçmişi Getir'}
              </Button>
              {hasSearched && historyData && historyData.length > 0 && (
                <Button 
                  variant="outline" 
                  onClick={handleExportReport}
                  className="bg-white/80 border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <DocumentArrowDownIcon className="w-5 h-5 mr-2" />
                  Dışa Aktar
                </Button>
              )}
            </div>

            {searchQuery && searchResults && searchResults.length === 0 && (
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <p className="text-sm text-yellow-700">
                  ⚠️ Arama kriterlerinize uygun hükümlü bulunamadı.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Selected Convict Info */}
        {selectedConvict && (
          <Card className="mb-6 backdrop-blur-sm bg-white/70 border-white/20 shadow-xl">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600 mb-1">TC Kimlik No</p>
                  <p className="text-lg font-mono font-semibold text-gray-900">{selectedConvict.tc_no}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600 mb-1">Ad Soyad</p>
                  <p className="text-lg font-semibold text-gray-900">{selectedConvict.first_name} {selectedConvict.last_name}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600 mb-1">Denetim Başlangıç</p>
                  <p className="text-lg font-medium text-gray-900">{formatDate(selectedConvict.supervision_start_date)}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-600 mb-1">Toplam İmza</p>
                  <p className="text-2xl font-bold text-blue-600">{historyData?.length || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Signature History Results */}
        {hasSearched && (
          <Card className="backdrop-blur-sm bg-white/70 border-white/20 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl">
              <CardTitle className="text-gray-800 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                İmza Geçmişi Detayları
                {selectedConvict && (
                  <span className="ml-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                    {selectedConvict.first_name} {selectedConvict.last_name}
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <DataTable
                data={historyData || []}
                columns={columns}
                loading={isLoadingHistory}
                emptyMessage={
                  hasSearched 
                    ? "Seçilen hükümlü için imza kaydı bulunmuyor" 
                    : "İmza geçmişini görüntülemek için hükümlü seçip 'Geçmişi Getir' butonuna tıklayın"
                }
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
