// Test UI simülasyonu - ManageSignaturePeriodsPage gruplama görünümü
const Database = require('better-sqlite3');

// <PERSON> grouping fonksiyonunu kopyalayalım
function groupSignaturePeriods(periods) {
  const groups = new Map();
  
  // Group periods by date range, type, and time
  periods.forEach(period => {
    const key = `${period.start_date}_${period.end_date}_${period.frequency_type}_${period.time_start || ''}_${period.time_end || ''}`;
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(period);
  });
  
  // Convert groups to consolidated periods
  const groupedPeriods = Array.from(groups.values()).map(groupPeriods => {
    // Use the first period as base
    const basePeriod = groupPeriods[0];
    
    // Merge frequency values based on type
    let mergedFrequencyValue = '';
    if (basePeriod.frequency_type === 'WEEKLY') {
      // Collect all weekly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      mergedFrequencyValue = Array.from(allDays).join(',');
    } else if (basePeriod.frequency_type === 'MONTHLY_SPECIFIC') {
      // Collect all monthly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      // Sort numerically for monthly days
      const sortedDays = Array.from(allDays).sort((a, b) => parseInt(a) - parseInt(b));
      mergedFrequencyValue = sortedDays.join(',');
    } else {
      // For X_DAYS type, use the first period's value
      mergedFrequencyValue = basePeriod.frequency_value;
    }
    
    // Return a merged period
    return {
      ...basePeriod,
      frequency_value: mergedFrequencyValue
    };
  });
  
  return groupedPeriods;
}

function getFrequencyText(frequencyType, frequencyValue) {
  switch (frequencyType) {
    case 'WEEKLY':
      const days = frequencyValue.split(',').map(day => {
        const dayMap = {
          'MONDAY': 'Pazartesi',
          'TUESDAY': 'Salı', 
          'WEDNESDAY': 'Çarşamba',
          'THURSDAY': 'Perşembe',
          'FRIDAY': 'Cuma',
          'SATURDAY': 'Cumartesi',
          'SUNDAY': 'Pazar'
        };
        return dayMap[day.trim()] || day;
      });
      return `Haftada ${days.length} Gün (${days.join(', ')})`;
    case 'MONTHLY_SPECIFIC':
      const monthDays = frequencyValue.split(',');
      return `Ayda ${monthDays.length} Gün (${monthDays.join(', ')}. günler)`;
    case 'X_DAYS':
      return `${frequencyValue} Günde Bir`;
    default:
      return frequencyValue;
  }
}

function formatDate(dateStr) {
  const date = new Date(dateStr);
  return date.toLocaleDateString('tr-TR');
}

console.log('🖥️  ManageSignaturePeriodsPage UI Simülasyonu');
console.log('=============================================');

// Database connection
const db = new Database('./database.sqlite', { readonly: true });

// Test convict (108 with test data)
const testConvictId = 108;

// Get signature periods for test convict
const periods = db.prepare(`
  SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, 
         reference_date, is_active, time_start, time_end, allowed_days
  FROM signature_periods 
  WHERE convict_id = ? AND is_active = 1
  ORDER BY start_date, frequency_type
`).all(testConvictId);

// Convert to expected format for grouping function
const formattedPeriods = periods.map(p => ({
  ...p,
  is_active: Boolean(p.is_active)
}));

// Apply grouping
const groupedPeriods = groupSignaturePeriods(formattedPeriods);

console.log(`\n📊 İmza Periyotları Yönetimi (Hükümlü ${testConvictId})`);
console.log('═'.repeat(80));
console.log('| Başlangıç     | Bitiş         | Sıklık                      | Saat Kısıt.  | Durum  | Aksiyon |');
console.log('|'.repeat(80));

groupedPeriods.forEach((period, index) => {
  const startDate = formatDate(period.start_date).padEnd(13);
  const endDate = formatDate(period.end_date).padEnd(13);
  const frequency = getFrequencyText(period.frequency_type, period.frequency_value).padEnd(27);
  const timeRestriction = (period.time_start && period.time_end) 
    ? `${period.time_start}-${period.time_end}`.padEnd(11)
    : 'Kısıt yok'.padEnd(11);
  const status = (period.is_active ? '✅ Aktif' : '⏸️ Pasif').padEnd(6);
  
  // Find matching original periods for action count
  const matchingOriginals = periods.filter(p => 
    p.start_date === period.start_date &&
    p.end_date === period.end_date &&
    p.frequency_type === period.frequency_type &&
    p.time_start === period.time_start &&
    p.time_end === period.time_end
  );
  
  const isGrouped = matchingOriginals.length > 1;
  const actionText = isGrouped ? `✏️ 🗑️(${matchingOriginals.length})` : '✏️ 🗑️';
  
  console.log(`| ${startDate} | ${endDate} | ${frequency} | ${timeRestriction} | ${status} | ${actionText} |`);
});

console.log('|'.repeat(80));

console.log('\n📝 Açıklamalar:');
console.log('• ✏️ = Düzenle (grupta sadece ilk periyot düzenlenir)');
console.log('• 🗑️ = Sil');
console.log('• 🗑️(N) = N adet gruplanmış periyot birlikte silinir');
console.log('• Saat Kısıt. = Zaman kısıtlamaları (imza saatleri)');

console.log('\n🔍 Öncesi vs Sonrası Karşılaştırma:');
console.log(`ÖNCE (Ayrı satırlar): ${periods.length} satır`);
periods.forEach((period, index) => {
  console.log(`  ${index + 1}. ${formatDate(period.start_date)} - ${getFrequencyText(period.frequency_type, period.frequency_value)}`);
});

console.log(`\nSONRA (Gruplanmış): ${groupedPeriods.length} satır`);
groupedPeriods.forEach((period, index) => {
  console.log(`  ${index + 1}. ${formatDate(period.start_date)} - ${getFrequencyText(period.frequency_type, period.frequency_value)}`);
});

console.log('\n✅ ManageSignaturePeriodsPage gruplama implementasyonu başarıyla tamamlandı!');
console.log('   Kullanıcı artık gruplanmış görünümde imza periyotlarını yönetebilir.');

db.close();
