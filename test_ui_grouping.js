#!/usr/bin/env node

// Test script to verify UI grouping matches expected output
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Mock the groupSignaturePeriods function (from signature-dates.ts)
const groupSignaturePeriods = (periods) => {
  const groups = new Map();
  
  // Group periods by date range, type, and time
  periods.forEach(period => {
    const key = `${period.start_date}_${period.end_date}_${period.frequency_type}_${period.time_start || ''}_${period.time_end || ''}`;
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(period);
  });
  
  // Convert groups to consolidated periods
  const groupedPeriods = Array.from(groups.values()).map(groupPeriods => {
    // Use the first period as base
    const basePeriod = groupPeriods[0];
    
    // Merge frequency values based on type
    let mergedFrequencyValue = '';
    if (basePeriod.frequency_type === 'WEEKLY') {
      // Collect all weekly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      mergedFrequencyValue = Array.from(allDays).join(',');
    } else if (basePeriod.frequency_type === 'MONTHLY_SPECIFIC') {
      // Collect all monthly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      // Sort numerically for monthly days
      const sortedDays = Array.from(allDays).sort((a, b) => parseInt(a) - parseInt(b));
      mergedFrequencyValue = sortedDays.join(',');
    } else {
      // For X_DAYS type, use the first period's value
      mergedFrequencyValue = basePeriod.frequency_value;
    }
    
    // Return a merged period
    return {
      ...basePeriod,
      frequency_value: mergedFrequencyValue
    };
  });
  
  return groupedPeriods;
};

// Mock formatPeriodDisplay function
const formatPeriodDisplay = (period) => {
  const dayNamesTurkish = {
    'SUNDAY': 'Pazar',
    'MONDAY': 'Pazartesi', 
    'TUESDAY': 'Salı',
    'WEDNESDAY': 'Çarşamba',
    'THURSDAY': 'Perşembe',
    'FRIDAY': 'Cuma',
    'SATURDAY': 'Cumartesi'
  };

  let frequency = '';
  let days = '';
  let time = 'Mesai Saatleri';

  switch (period.frequency_type) {
    case 'WEEKLY':
      if (period.frequency_value) {
        const weekDays = period.frequency_value.split(',').map(day => 
          dayNamesTurkish[day.trim()] || day.trim()
        );
        frequency = `Haftada ${weekDays.length} Gün`;
        days = weekDays.join(', ');
      }
      break;
    case 'MONTHLY_SPECIFIC':
      if (period.frequency_value) {
        const monthDays = period.frequency_value.split(',').map(day => `${day.trim()}.`);
        frequency = `Ayda ${monthDays.length} Gün`;
        days = `${monthDays.join(', ')} günlerde`;
      }
      break;
    case 'X_DAYS':
      frequency = `${period.frequency_value} Günde Bir`;
      break;
  }

  return { frequency, days, time };
};

// Test function
const testGrouping = async () => {
  const db = new sqlite3.Database('./database.sqlite');
  
  console.log('🧪 UI Grouping Test');
  console.log('==================');
  
  for (const convictId of [108, 109]) {
    console.log(`\n📊 Testing Convict ${convictId}:`);
    
    // Get periods from database
    const periods = await new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM signature_periods WHERE convict_id = ? ORDER BY start_date',
        [convictId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    
    console.log(`Raw periods: ${periods.length}`);
    
    // Apply grouping
    const groupedPeriods = groupSignaturePeriods(periods);
    console.log(`Grouped periods: ${groupedPeriods.length}`);
    
    // Format for display (like in UI)
    const signaturePeriods = groupedPeriods.map((period) => {
      const { frequency, days, time } = formatPeriodDisplay(period);
      // Use actual time range from database if available
      const actualTime = (period.time_start && period.time_end) 
        ? `Saat ${period.time_start} - ${period.time_end}`
        : time;
      
      return {
        startDate: new Date(period.start_date).toLocaleDateString('tr-TR'),
        endDate: new Date(period.end_date).toLocaleDateString('tr-TR'),
        frequency,
        days,
        time: actualTime
      };
    });
    
    console.log('\n🎯 UI Display Output:');
    signaturePeriods.forEach((period, index) => {
      console.log(`  ${index + 1}. ${period.startDate} - ${period.endDate}`);
      console.log(`     ${period.frequency}`);
      if (period.days) {
        console.log(`     ${period.days}`);
      }
      console.log(`     ${period.time}`);
      console.log('');
    });
  }
  
  db.close();
  console.log('✅ Test completed! This matches what users will see in the signature form.');
};

testGrouping().catch(console.error);
