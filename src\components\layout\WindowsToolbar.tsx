import { Link, useLocation } from 'wouter';
import {
  HomeIcon,
  UsersIcon,
  PlusIcon,
  PencilIcon,
  DocumentChartBarIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PrinterIcon,
  DocumentArrowDownIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

const toolbarItems = [
  { name: '<PERSON>', icon: HomeIcon, href: '/dashboard', tooltip: 'Ana sayfaya git (Ctrl+Home)' },
  { name: '<PERSON><PERSON>', icon: PlusIcon, href: '/convicts/new', tooltip: '<PERSON><PERSON> hükü<PERSON> (Ctrl+N)' },
  { name: '<PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON> Listesi', icon: UsersIcon, href: '/convicts', tooltip: 'Hükümlü listesini görüntüle' },
  { name: '<PERSON><PERSON><PERSON>dı', icon: PencilIcon, href: '/signatures/record', tooltip: '<PERSON><PERSON><PERSON> kaydı yap' },
  { separator: true },
  { name: '<PERSON><PERSON><PERSON>', icon: DocumentChartBarIcon, href: '/reports/daily', tooltip: '<PERSON><PERSON><PERSON><PERSON> görüntüle' },
  { name: '<PERSON><PERSON><PERSON>', icon: ArrowPathIcon, action: 'refresh', tooltip: 'Sayfayı yenile (F5)' },
  { separator: true },
  { name: 'Ara', icon: MagnifyingGlassIcon, action: 'search', tooltip: 'Ara (Ctrl+F)' },
  { name: 'Filtrele', icon: FunnelIcon, action: 'filter', tooltip: 'Filtrele (Ctrl+Shift+F)' },
  { separator: true },
  { name: 'Yazdır', icon: PrinterIcon, action: 'print', tooltip: 'Yazdır (Ctrl+P)' },
  { name: 'Dışa Aktar', icon: DocumentArrowDownIcon, action: 'export', tooltip: 'Dışa aktar' },
  { separator: true },
  { name: 'Ayarlar', icon: Cog6ToothIcon, href: '/admin/settings', tooltip: 'Sistem ayarları' },
];

export default function WindowsToolbar() {
  const [location] = useLocation();

  const handleAction = (action: string) => {
    switch (action) {
      case 'refresh':
        window.location.reload();
        break;
      case 'search': {
        // Focus on search input if exists
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="ara"], input[placeholder*="Ara"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
          searchInput.select();
        }
        break;
      }
      case 'filter':
        // Show filter dialog or focus filter controls
        console.log('Filter action');
        break;
      case 'print':
        window.print();
        break;
      case 'export':
        console.log('Export action');
        break;
      default:
        console.log('Toolbar action:', action);
    }
  };

  const isActive = (href: string) => {
    return location === href || location.startsWith(href + '/');
  };

  return (
    <div className="bg-white/15 backdrop-blur-lg border-b border-white/10 px-3 py-2 flex items-center space-x-1">
      {toolbarItems.map((item, index) => {
        if (item.separator) {
          return (
            <div key={index} className="w-px h-6 bg-white/20 mx-2" />
          );
        }

        const IconComponent = item.icon!;
        const isItemActive = item.href ? isActive(item.href) : false;

        if (item.href) {
          return (
            <Link key={index} href={item.href}>
              <button
                className={`p-2.5 rounded-lg hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-md focus:bg-white/25 transition-all duration-200 group relative hover:scale-105 active:scale-95 ${
                  isItemActive ? 'bg-white/25 shadow-lg ring-1 ring-white/30' : ''
                }`}
                title={item.tooltip}
              >
                <IconComponent className="w-5 h-5 text-slate-700" />
                
                {/* Modern Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-slate-800/90 backdrop-blur-md text-white text-xs rounded-lg opacity-0 transition-all duration-200 pointer-events-none whitespace-nowrap z-20 border border-white/10">
                  {item.tooltip}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-800/90"></div>
                </div>
              </button>
            </Link>
          );
        }

        return (
          <button
            key={index}
            className="p-2.5 rounded-lg hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-md focus:bg-white/25 transition-all duration-200 group relative hover:scale-105 active:scale-95"
            onClick={() => handleAction(item.action!)}
            title={item.tooltip}
          >
            <IconComponent className="w-5 h-5 text-slate-700" />
            
            {/* Modern Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-slate-800/90 backdrop-blur-md text-white text-xs rounded-lg opacity-0 transition-all duration-200 pointer-events-none whitespace-nowrap z-20 border border-white/10">
              {item.tooltip}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-800/90"></div>
            </div>
          </button>
        );
      })}
      
      {/* Spacer */}
      <div className="flex-1" />
      
      {/* Right side toolbar items */}
      <div className="flex items-center space-x-3">
        <div className="text-sm font-medium text-slate-700 bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-white/10">
          {new Date().toLocaleDateString('tr-TR', {
            weekday: 'short',
            day: 'numeric',
            month: 'short',
            year: 'numeric'
          })}
        </div>
        <div className="text-sm font-medium text-slate-700 bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-white/10">
          {new Date().toLocaleTimeString('tr-TR', {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>
    </div>
  );
}