import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { MagnifyingGlassIcon, DocumentTextIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { searchConvictByTcNo, searchConvictsByName, searchConvictsByFileNumber } from '@/lib/tauri-api';
import type { Convict } from '@shared/schema';

export default function SignatureFormSearchPage() {
  const [searchType, setSearchType] = useState<'name' | 'tc_no' | 'file_number'>('name');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Convict[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      return;
    }

    setIsSearching(true);
    setHasSearched(true);
    
    try {
      let results: Convict[] = [];
      
      if (searchType === 'tc_no') {
        const convict = await searchConvictByTcNo(searchQuery.trim());
        results = convict ? [convict] : [];
      } else if (searchType === 'name') {
        const nameParts = searchQuery.trim().split(' ').filter(part => part.length > 0);
        if (nameParts.length >= 1) {
          const firstName = nameParts[0] || "";
          const lastName = nameParts.slice(1).join(' ') || "";
          results = await searchConvictsByName(firstName, lastName);
        }
      } else if (searchType === 'file_number') {
        results = await searchConvictsByFileNumber(searchQuery.trim());
      }
      
      setSearchResults(results);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getSearchPlaceholder = () => {
    switch (searchType) {
      case 'tc_no':
        return 'TC Kimlik No ile ara...';
      case 'name':
        return 'Ad Soyad ile ara...';
      case 'file_number':
        return 'Dosya numarası ile ara...';
      default:
        return 'Arama yapın...';
    }
  };

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">İmza Föyü - Hükümlü Ara</h1>
            <span className="text-gray-600 text-xs ml-2">
              - Hükümlü seçerek imza föyüne erişin
            </span>
          </div>
        </div>
      </div>

      <div className="p-3">
        {/* Search Section */}
        <div className="windows-card mb-4">
          <div className="windows-section-title">Hükümlü Arama</div>
          <div className="p-4 space-y-4">
            {/* Search Type Selection */}
            <div className="space-y-2">
              <Label htmlFor="searchType" className="windows-label">Arama Türü</Label>
              <Select value={searchType} onValueChange={(value: 'name' | 'tc_no' | 'file_number') => setSearchType(value)}>
                <SelectTrigger className="windows-select">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Ad Soyad</SelectItem>
                  <SelectItem value="tc_no">TC Kimlik No</SelectItem>
                  <SelectItem value="file_number">Dosya No</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Search Input */}
            <div className="space-y-2">
              <Label htmlFor="searchInput" className="windows-label">
                {searchType === 'tc_no' ? 'TC Kimlik Numarası' : 
                 searchType === 'name' ? 'Ad Soyad' : 'Dosya Numarası'}
              </Label>
              <div className="flex gap-2">
                <Input
                  id="searchInput"
                  placeholder={getSearchPlaceholder()}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isSearching}
                  className="windows-input flex-1"
                />
                <Button 
                  onClick={handleSearch} 
                  disabled={isSearching || !searchQuery.trim()}
                  className="windows-button-primary"
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-1" />
                  {isSearching ? 'Aranıyor...' : 'Ara'}
                </Button>
              </div>
            </div>

            {/* Search hint */}
            <div className="text-xs text-gray-600">
              {searchType === 'tc_no' && 'TC Kimlik numarasının tamamını girin'}
              {searchType === 'name' && 'Ad ve soyadın bir kısmını veya tamamını yazabilirsiniz'}
              {searchType === 'file_number' && 'Dosya numarasının bir kısmını veya tamamını yazabilirsiniz'}
            </div>
          </div>
        </div>

        {/* Search Results */}
        {hasSearched && (
          <div className="windows-card">
            <div className="windows-section-title">
              Arama Sonuçları
              {searchResults.length > 0 && ` (${searchResults.length} hükümlü)`}
            </div>
            <div className="p-4">
              {isSearching ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="lg" />
                  <span className="ml-3 text-gray-600">Aranıyor...</span>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-3">
                  {searchResults.map((convict) => (
                    <Card key={convict.id} className="border border-gray-200 hover:border-blue-300 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center gap-4">
                              <h3 className="font-medium text-gray-900">
                                {convict.first_name} {convict.last_name}
                              </h3>
                              <span className={`inline-block px-2 py-1 text-xs rounded ${
                                convict.is_active 
                                  ? 'bg-green-100 text-green-800 border border-green-200' 
                                  : 'bg-gray-100 text-gray-800 border border-gray-200'
                              }`}>
                                {convict.is_active ? 'Aktif' : 'Pasif'}
                              </span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                              <div>
                                <span className="font-medium">TC No:</span> {convict.tc_no}
                              </div>
                              {convict.file_number && (
                                <div>
                                  <span className="font-medium">Dosya No:</span> {convict.file_number}
                                </div>
                              )}
                              <div>
                                <span className="font-medium">Denetim:</span> {convict.supervision_start_date} - {convict.supervision_end_date}
                              </div>
                              {convict.phone_number && (
                                <div>
                                  <span className="font-medium">Telefon:</span> {convict.phone_number}
                                </div>
                              )}
                              {convict.address && (
                                <div className="col-span-full">
                                  <span className="font-medium">Adres:</span> {convict.address}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Link href={`/convicts/${convict.id}/signature-form`}>
                              <Button className="windows-button-primary">
                                <DocumentTextIcon className="w-4 h-4 mr-2" />
                                İmza Föyü
                                <ArrowRightIcon className="w-4 h-4 ml-1" />
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Alert>
                  <MagnifyingGlassIcon className="h-4 w-4" />
                  <AlertDescription>
                    {searchType === 'tc_no' && 'Bu TC Kimlik numarası ile kayıtlı hükümlü bulunamadı.'}
                    {searchType === 'name' && 'Bu ad soyad ile eşleşen hükümlü bulunamadı.'}
                    {searchType === 'file_number' && 'Bu dosya numarası ile eşleşen hükümlü bulunamadı.'}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        )}

        {/* Instructions */}
        {!hasSearched && (
          <div className="windows-card">
            <div className="windows-section-title">Nasıl Kullanılır?</div>
            <div className="p-4 space-y-3 text-sm text-gray-600">
              <div className="flex items-start gap-2">
                <span className="font-medium text-blue-600 min-w-6">1.</span>
                <span>Yukarıdan arama türünü seçin (Ad Soyad, TC Kimlik No veya Dosya No)</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium text-blue-600 min-w-6">2.</span>
                <span>Arama kutusuna ilgili bilgiyi girin</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium text-blue-600 min-w-6">3.</span>
                <span>"Ara" butonuna tıklayın veya Enter tuşuna basın</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium text-blue-600 min-w-6">4.</span>
                <span>Sonuçlardan istediğiniz hükümlünün "İmza Föyü" butonuna tıklayın</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
