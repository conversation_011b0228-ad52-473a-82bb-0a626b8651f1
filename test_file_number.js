#!/usr/bin/env node

/**
 * Test script to verify file number functionality in the EITS application
 * This script tests:
 * 1. Adding a convict with a file number
 * 2. Retrieving the convict and verifying file number is stored
 * 3. Updating the convict's file number
 * 4. Verifying the update persisted
 */

const { join } = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

const DB_PATH = join(__dirname, 'database.sqlite');

async function runSQLQuery(query) {
  try {
    const { stdout, stderr } = await execAsync(`sqlite3 "${DB_PATH}" "${query}"`);
    if (stderr) {
      console.error('SQL Error:', stderr);
      return null;
    }
    return stdout.trim();
  } catch (error) {
    console.error('Query execution failed:', error);
    return null;
  }
}

async function testFileNumberFunctionality() {
  console.log('🧪 Testing File Number Functionality...\n');

  // Test 1: Check current convicts and their file numbers
  console.log('📋 Current convicts with file numbers:');
  const existingConvicts = await runSQLQuery(
    "SELECT tc_no, first_name, last_name, file_number FROM convicts WHERE file_number IS NOT NULL AND file_number != '';"
  );
  
  if (existingConvicts) {
    console.log(existingConvicts || 'No convicts with file numbers found');
  } else {
    console.log('No convicts with file numbers found');
  }
  console.log('');

  // Test 2: Update an existing convict with a file number
  console.log('✏️  Test 2: Adding file number to existing convict...');
  
  // Get the first convict
  const firstConvict = await runSQLQuery(
    "SELECT id, tc_no, first_name, last_name FROM convicts LIMIT 1;"
  );
  
  if (firstConvict) {
    const [id, tc_no, first_name, last_name] = firstConvict.split('|');
    const testFileNumber = '2025/TEST-001 NKL';
    
    console.log(`Updating convict: ${first_name} ${last_name} (TC: ${tc_no})`);
    console.log(`Setting file number to: ${testFileNumber}`);
    
    // Update the convict with a file number
    const updateResult = await runSQLQuery(
      `UPDATE convicts SET file_number = '${testFileNumber}' WHERE id = ${id};`
    );
    
    if (updateResult !== null) {
      console.log('✅ File number update executed');
      
      // Verify the update
      const verifyResult = await runSQLQuery(
        `SELECT tc_no, first_name, last_name, file_number FROM convicts WHERE id = ${id};`
      );
      
      if (verifyResult) {
        const [verify_tc, verify_first, verify_last, verify_file_number] = verifyResult.split('|');
        console.log(`✅ Verification: ${verify_first} ${verify_last} (TC: ${verify_tc}) -> File Number: ${verify_file_number}`);
        
        if (verify_file_number === testFileNumber) {
          console.log('✅ File number correctly stored and retrieved!');
        } else {
          console.log('❌ File number mismatch!');
        }
      } else {
        console.log('❌ Could not verify the update');
      }
    } else {
      console.log('❌ File number update failed');
    }
  } else {
    console.log('❌ No convicts found to test with');
  }
  console.log('');

  // Test 3: Check all convicts with file numbers after update
  console.log('📋 All convicts with file numbers after test:');
  const allWithFileNumbers = await runSQLQuery(
    "SELECT tc_no, first_name, last_name, file_number FROM convicts WHERE file_number IS NOT NULL AND file_number != '';"
  );
  
  if (allWithFileNumbers) {
    console.log(allWithFileNumbers);
  } else {
    console.log('No convicts with file numbers found');
  }
  console.log('');

  // Test 4: Database schema verification
  console.log('🗃️  Database schema verification:');
  const schemaInfo = await runSQLQuery(
    "PRAGMA table_info(convicts);"
  );
  
  if (schemaInfo) {
    const lines = schemaInfo.split('\n');
    const fileNumberColumn = lines.find(line => line.includes('file_number'));
    
    if (fileNumberColumn) {
      console.log('✅ file_number column exists in convicts table');
      console.log(`   Column info: ${fileNumberColumn}`);
    } else {
      console.log('❌ file_number column not found in convicts table');
    }
  } else {
    console.log('❌ Could not retrieve schema information');
  }

  console.log('\n🎯 File Number Test Complete!');
}

// Run the test
testFileNumberFunctionality().catch(console.error);
