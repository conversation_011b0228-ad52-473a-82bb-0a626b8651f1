import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BookOpenIcon, 
  UserGroupIcon, 
  PencilIcon, 
  DocumentChartBarIcon,
  Cog6ToothIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  KeyIcon,
  ShieldCheckIcon,
  CalendarDaysIcon,
  UserPlusIcon,
  CircleStackIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const UsageGuidePage = () => {
  const [activeSection, setActiveSection] = useState('overview');

  const sections = [
    { id: 'overview', title: '<PERSON>l Bakış', icon: BookOpenIcon },
    { id: 'getting-started', title: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: UserPlusIcon },
    { id: 'convicts', title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>önetim<PERSON>', icon: UserGroupIcon },
    { id: 'signatures', title: '<PERSON><PERSON><PERSON>', icon: PencilIcon },
    { id: 'reports', title: '<PERSON><PERSON><PERSON>', icon: DocumentChartBarIcon },
    { id: 'excel-import', title: 'Excel İçe/Dışa Aktarma', icon: ArrowDownTrayIcon },
    { id: 'admin', title: 'Yönetici İşlemleri', icon: Cog6ToothIcon },
    { id: 'shortcuts', title: 'Klavye Kısayolları', icon: KeyIcon },
    { id: 'troubleshooting', title: 'Sorun Giderme', icon: ExclamationTriangleIcon }
  ];

  return (
    <div className="h-full flex flex-col p-6 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
          <BookOpenIcon className="w-8 h-8 text-blue-600" />
          Elektronik İmza Takip Sistemi - Kullanım Kılavuzu
        </h1>
        <p className="text-gray-600">
          Bu kılavuz, sistemin tüm özelliklerini etkin bir şekilde kullanmanıza yardımcı olacaktır.
        </p>
      </div>

      <div className="flex flex-1 gap-6">
        {/* Sol menü */}
        <div className="w-80 bg-white rounded-lg shadow-md p-4">
          <h3 className="font-semibold text-gray-900 mb-4">İçindekiler</h3>
          <div className="space-y-2">
            {sections.map((section) => {
              const IconComponent = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center gap-3 ${
                    activeSection === section.id
                      ? 'bg-blue-100 text-blue-700 font-medium'
                      : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  {section.title}
                </button>
              );
            })}
          </div>
        </div>

        {/* Ana içerik */}
        <div className="flex-1 bg-white rounded-lg shadow-md">
          <ScrollArea className="h-full">
            <div className="p-6">
              {activeSection === 'overview' && <OverviewSection />}
              {activeSection === 'getting-started' && <GettingStartedSection />}
              {activeSection === 'convicts' && <ConvictsSection />}
              {activeSection === 'signatures' && <SignaturesSection />}
              {activeSection === 'reports' && <ReportsSection />}
              {activeSection === 'excel-import' && <ExcelImportSection />}
              {activeSection === 'admin' && <AdminSection />}
              {activeSection === 'shortcuts' && <ShortcutsSection />}
              {activeSection === 'troubleshooting' && <TroubleshootingSection />}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};

const OverviewSection = () => (
  <div className="space-y-6">
    <div>
      <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
        <InformationCircleIcon className="w-7 h-7 text-blue-600" />
        Sistem Hakkında
      </h2>
      <p className="text-gray-700 leading-relaxed mb-4">
        Elektronik İmza Takip Sistemi, hükümlü ve tutukluların denetimli serbestlik süreçlerinde 
        elektronik imza yükümlülüklerini takip etmek için geliştirilmiş kapsamlı bir uygulamadır.
      </p>
    </div>

    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircleIcon className="w-5 h-5 text-green-600" />
          Temel Özellikler
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <UserGroupIcon className="w-5 h-5 text-blue-600" />
              <span>Hükümlü kayıt ve yönetimi</span>
            </div>
            <div className="flex items-center gap-3">
              <PencilIcon className="w-5 h-5 text-green-600" />
              <span>İmza kaydı ve takibi</span>
            </div>
            <div className="flex items-center gap-3">
              <CalendarDaysIcon className="w-5 h-5 text-purple-600" />
              <span>İmza periyot yönetimi</span>
            </div>
            <div className="flex items-center gap-3">
              <ClockIcon className="w-5 h-5 text-orange-600" />
              <span>Muafiyet takibi</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <DocumentChartBarIcon className="w-5 h-5 text-red-600" />
              <span>Detaylı raporlama</span>
            </div>
            <div className="flex items-center gap-3">
              <ArrowDownTrayIcon className="w-5 h-5 text-indigo-600" />
              <span>Excel içe/dışa aktarma</span>
            </div>
            <div className="flex items-center gap-3">
              <ShieldCheckIcon className="w-5 h-5 text-emerald-600" />
              <span>Kullanıcı yetki yönetimi</span>
            </div>
            <div className="flex items-center gap-3">
              <CircleStackIcon className="w-5 h-5 text-cyan-600" />
              <span>Veri yedekleme ve geri yükleme</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Kullanıcı Rolleri</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Badge variant="default">ADMIN</Badge>
            <span>Tam yetki - Tüm modüllere erişim, kullanıcı yönetimi, sistem ayarları</span>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary">MEMUR</Badge>
            <span>Sınırlı yetki - Hükümlü yönetimi, imza kayıt ve raporlama</span>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

const GettingStartedSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <UserPlusIcon className="w-7 h-7 text-green-600" />
      Başlangıç Rehberi
    </h2>

    <Card>
      <CardHeader>
        <CardTitle>1. Sisteme Giriş</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• Kullanıcı adı ve şifrenizi girerek sisteme giriş yapın</p>
          <p>• İlk girişte şifrenizi değiştirmeniz önerilir</p>
          <p>• Güvenlik için oturumunuzu kullanım sonrası kapatmayı unutmayın</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>2. Ana Sayfa Tanıtımı</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Dashboard:</strong> Güncel istatistikler ve hızlı erişim menüleri</p>
          <p>• <strong>Sol Panel:</strong> Ana menü sistemi (hükümlü, imza, raporlar)</p>
          <p>• <strong>Üst Menü:</strong> Dosya, düzenle, görünüm, araçlar ve yardım menüleri</p>
          <p>• <strong>Araç Çubuğu:</strong> Sık kullanılan işlemler için hızlı erişim butonları</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>3. İlk Kurulum Adımları</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• Sistem ayarlarını kontrol edin (Araçlar → Sistem Ayarları)</p>
          <p>• Kullanıcı hesaplarını oluşturun (sadece ADMIN)</p>
          <p>• İlk hükümlü kayıtlarını sisteme ekleyin</p>
          <p>• İmza periyotlarını tanımlayın</p>
        </div>
      </CardContent>
    </Card>
  </div>
);

const ConvictsSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <UserGroupIcon className="w-7 h-7 text-blue-600" />
      Hükümlü Yönetimi
    </h2>

    <Card>
      <CardHeader>
        <CardTitle>Yeni Hükümlü Ekleme</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Menü:</strong> Hükümlü Yönetimi → Yeni Hükümlü veya Ctrl+N</p>
          <p>• <strong>Gerekli Bilgiler:</strong> TC Kimlik No, Ad-Soyad, Dosya No</p>
          <p>• <strong>İsteğe Bağlı:</strong> Adres, telefon, e-posta bilgileri</p>
          <p>• <strong>Doğrulama:</strong> TC Kimlik No kontrolü otomatik yapılır</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Hükümlü Bilgilerini Düzenleme</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• Hükümlü listesinde ilgili kayda çift tıklayın</p>
          <p>• Veya kayıt seçip "Düzenle" butonuna basın</p>
          <p>• Değişiklikleri kaydettikten sonra sistem otomatik güncellenir</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>İmza Periyot Yönetimi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Periyot Türleri:</strong> Haftalık, Aylık, İki Aylık</p>
          <p>• <strong>Başlangıç-Bitiş Tarihleri:</strong> Denetim süresini belirler</p>
          <p>• <strong>Otomatik Hesaplama:</strong> İmza günleri sistem tarafından hesaplanır</p>
          <p>• <strong>Çakışma Kontrolü:</strong> Aynı dönemde birden fazla periyot engellenmiştir</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Muafiyet Yönetimi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Sağlık Raporu:</strong> Hastane raporu ile imza muafiyeti</p>
          <p>• <strong>İzin:</strong> Geçici izin süreleri</p>
          <p>• <strong>Otomatik Hesaplama:</strong> Muaf günler imza yükümlülüğünden düşülür</p>
          <p>• <strong>Belge Takibi:</strong> Muafiyet belgelerinin kayıt altında tutulması</p>
        </div>
      </CardContent>
    </Card>
  </div>
);

const SignaturesSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <PencilIcon className="w-7 h-7 text-green-600" />
      İmza İşlemleri
    </h2>

    <Card>
      <CardHeader>
        <CardTitle>İmza Kaydı</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Hızlı Arama:</strong> TC No veya Ad-Soyad ile hükümlü bulun</p>
          <p>• <strong>Tarih Seçimi:</strong> İmza tarihi (varsayılan: bugün)</p>
          <p>• <strong>Saat Kaydı:</strong> İmza saati otomatik kaydedilir</p>
          <p>• <strong>Toplu İşlem:</strong> Birden fazla hükümlü için toplu imza kaydı</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Beklenen İmzalar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Günlük Liste:</strong> Bugün imza vermesi gereken hükümlüler</p>
          <p>• <strong>Geciken İmzalar:</strong> Süresi geçen imzalar kırmızı renkte</p>
          <p>• <strong>Filtreleme:</strong> Tarih aralığı ve duruma göre filtreleme</p>
          <p>• <strong>Dışa Aktarma:</strong> Listeyi Excel formatında kaydetme</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Tamamlanan İmzalar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Geçmiş Kayıtlar:</strong> Verilen tüm imzaların listesi</p>
          <p>• <strong>Düzenleme:</strong> Yanlış kayıtların düzeltilmesi</p>
          <p>• <strong>Silme İşlemi:</strong> Hatalı kayıtların silinmesi (dikkatli olun!)</p>
          <p>• <strong>Arama:</strong> Tarih, hükümlü veya dosya no ile arama</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>İmza Föyü</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Bireysel Takip:</strong> Her hükümlü için detaylı imza geçmişi</p>
          <p>• <strong>Dönemsel Görünüm:</strong> İmza periyoduna göre listeleme</p>
          <p>• <strong>İstatistikler:</strong> Toplam, verilen, eksik imza sayıları</p>
          <p>• <strong>Yazdırma:</strong> Hükümlü imza föyünü yazdırma</p>
        </div>
      </CardContent>
    </Card>
  </div>
);

const ReportsSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <DocumentChartBarIcon className="w-7 h-7 text-red-600" />
      Raporlama Sistemi
    </h2>

    <Card>
      <CardHeader>
        <CardTitle>Günlük Takip Raporu</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Günlük Özet:</strong> Gün bazında imza istatistikleri</p>
          <p>• <strong>Verilen İmzalar:</strong> Bugün sisteme kaydedilen imzalar</p>
          <p>• <strong>Beklenen İmzalar:</strong> Bugün verilmesi gereken imzalar</p>
          <p>• <strong>Geciken İmzalar:</strong> Süresi geçen imza yükümlülükleri</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Hükümlü Geçmişi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Bireysel Analiz:</strong> Seçilen hükümlünün tüm imza geçmişi</p>
          <p>• <strong>Tarih Aralığı:</strong> Belirli dönem için filtreleme</p>
          <p>• <strong>Grafik Görünüm:</strong> İmza düzenini görsel olarak takip</p>
          <p>• <strong>Detay Bilgi:</strong> Her imzanın tarih, saat ve durum bilgisi</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>İhlal Raporu</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>İhlal Türleri:</strong> Geç imza, eksik imza, hiç gelmeme</p>
          <p>• <strong>Kritik Seviyeler:</strong> İhlal sayısına göre uyarı seviyeleri</p>
          <p>• <strong>Toplu Analiz:</strong> Tüm hükümlülerin ihlal durumu</p>
          <p>• <strong>Resmi Rapor:</strong> Yazdırılabilir resmi rapor formatı</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>İstatistiksel Raporlar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Aylık Özet:</strong> Ay bazında genel istatistikler</p>
          <p>• <strong>Başarı Oranları:</strong> İmza verme başarı yüzdeleri</p>
          <p>• <strong>Trend Analizi:</strong> Zaman içindeki değişim grafikleri</p>
          <p>• <strong>Karşılaştırma:</strong> Dönemler arası karşılaştırmalı analiz</p>
        </div>
      </CardContent>
    </Card>
  </div>
);

const ExcelImportSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <ArrowDownTrayIcon className="w-7 h-7 text-indigo-600" />
      Excel İçe/Dışa Aktarma
    </h2>

    <Card>
      <CardHeader>
        <CardTitle className="text-red-600 flex items-center gap-2">
          <ExclamationTriangleIcon className="w-5 h-5" />
          Önemli Uyarılar
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 text-red-700">
          <p>• İçe aktarma işlemi öncesi mutlaka veri yedeği alın</p>
          <p>• Excel dosyasının formatının doğru olduğundan emin olun</p>
          <p>• Büyük dosyalar için işlem süresi uzun olabilir</p>
          <p>• İşlem sırasında sistemi kapatmayın</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Excel Dosyası İçe Aktarma</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Desteklenen Formatlar:</h4>
            <p>• .xlsx (Excel 2007 ve üzeri)</p>
            <p>• .xls (Excel 97-2003)</p>
            <p>• .csv (UTF-8 kodlaması önerilir)</p>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Gerekli Sütunlar:</h4>
            <div className="bg-gray-50 p-3 rounded">
              <p><strong>TC Kimlik No:</strong> 11 haneli, zorunlu</p>
              <p><strong>Ad:</strong> Hükümlünün adı, zorunlu</p>
              <p><strong>Soyad:</strong> Hükümlünün soyadı, zorunlu</p>
              <p><strong>Dosya No:</strong> Benzersiz dosya numarası, zorunlu</p>
              <p><strong>İmza Periyodu:</strong> HAFTALIK, AYLIK, IKI_AYLIK</p>
              <p><strong>Başlangıç Tarihi:</strong> GG.AA.YYYY formatında</p>
              <p><strong>Bitiş Tarihi:</strong> GG.AA.YYYY formatında</p>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">İşlem Adımları:</h4>
            <ol className="list-decimal list-inside space-y-1">
              <li>Dosya → İçe Aktar menüsünden Excel dosyasını seçin</li>
              <li>Sütun eşleştirmesini kontrol edin</li>
              <li>Önizleme ekranında verileri gözden geçirin</li>
              <li>Hatalar varsa düzeltin veya dosyayı güncelleyin</li>
              <li>"İçe Aktar" butonuna basarak işlemi başlatın</li>
            </ol>
          </div>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Dışa Aktarma İşlemleri</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Hükümlü Listesi:</strong> Tüm hükümlü bilgilerini Excel formatında</p>
          <p>• <strong>İmza Kayıtları:</strong> Belirli tarih aralığındaki imza kayıtları</p>
          <p>• <strong>Raporlar:</strong> Günlük, aylık ve ihlal raporlarını Excel'e aktarma</p>
          <p>• <strong>Filtreli Dışa Aktarma:</strong> Sadece seçili kayıtları aktarma</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Sık Karşılaşılan Hatalar ve Çözümleri</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <p><strong>Hata:</strong> "TC Kimlik No geçersiz"</p>
            <p><strong>Çözüm:</strong> 11 haneli geçerli TC No kontrolü yapın</p>
          </div>
          <div>
            <p><strong>Hata:</strong> "Dosya No zaten mevcut"</p>
            <p><strong>Çözüm:</strong> Benzersiz dosya numaraları kullanın</p>
          </div>
          <div>
            <p><strong>Hata:</strong> "Tarih formatı hatalı"</p>
            <p><strong>Çözüm:</strong> GG.AA.YYYY formatını kullanın (örn: 15.03.2024)</p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

const AdminSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <Cog6ToothIcon className="w-7 h-7 text-purple-600" />
      Yönetici İşlemleri
    </h2>

    <Card>
      <CardHeader>
        <CardTitle className="text-orange-600 flex items-center gap-2">
          <ShieldCheckIcon className="w-5 h-5" />
          Yetki Uyarısı
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-orange-700">
          Bu bölümdeki işlemler sadece ADMIN yetkisine sahip kullanıcılar tarafından gerçekleştirilebilir.
        </p>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Kullanıcı Yönetimi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Yeni Kullanıcı:</strong> Sistem kullanıcıları oluşturma</p>
          <p>• <strong>Yetki Atama:</strong> ADMIN veya MEMUR rolü belirleme</p>
          <p>• <strong>Şifre Yönetimi:</strong> Kullanıcı şifrelerini sıfırlama</p>
          <p>• <strong>Hesap Durumu:</strong> Kullanıcı hesaplarını aktif/pasif yapma</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Sistem Ayarları</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Genel Ayarlar:</strong> Sistem geneli yapılandırma seçenekleri</p>
          <p>• <strong>İmza Ayarları:</strong> İmza periyotları ve tolerans süreleri</p>
          <p>• <strong>Bildirim Ayarları:</strong> E-posta ve sistem bildirimleri</p>
          <p>• <strong>Güvenlik Ayarları:</strong> Oturum süresi ve şifre politikaları</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Veri Yönetimi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Veri Yedekleme:</strong> Sistem verilerinin güvenli yedeğini alma</p>
          <p>• <strong>Geri Yükleme:</strong> Yedek dosyalarından veri geri yükleme</p>
          <p>• <strong>Veri Temizleme:</strong> Eski kayıtları arşivleme veya silme</p>
          <p>• <strong>Sistem Logları:</strong> Kullanıcı aktivitelerini ve sistem olaylarını izleme</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Sistem İzleme</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Performans:</strong> Sistem performansını izleme</p>
          <p>• <strong>Kullanım İstatistikleri:</strong> Kullanıcı aktivite raporları</p>
          <p>• <strong>Hata Takibi:</strong> Sistem hatalarını izleme ve çözme</p>
          <p>• <strong>Güvenlik Logları:</strong> Giriş/çıkış ve yetki ihlali kayıtları</p>
        </div>
      </CardContent>
    </Card>
  </div>
);

const ShortcutsSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <KeyIcon className="w-7 h-7 text-gray-600" />
      Klavye Kısayolları
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Sistem Kısayolları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Yardım (Bu sayfa)</span>
              <Badge variant="outline">F1</Badge>
            </div>
            <div className="flex justify-between">
              <span>Sayfayı yenile</span>
              <Badge variant="outline">F5</Badge>
            </div>
            <div className="flex justify-between">
              <span>Yazdır</span>
              <Badge variant="outline">Ctrl+P</Badge>
            </div>
            <div className="flex justify-between">
              <span>Arama</span>
              <Badge variant="outline">Ctrl+F</Badge>
            </div>
            <div className="flex justify-between">
              <span>Filtrele</span>
              <Badge variant="outline">Ctrl+Shift+F</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Navigasyon Kısayolları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Ana sayfa</span>
              <Badge variant="outline">Ctrl+Home</Badge>
            </div>
            <div className="flex justify-between">
              <span>Hükümlü listesi</span>
              <Badge variant="outline">Ctrl+L</Badge>
            </div>
            <div className="flex justify-between">
              <span>İmza kaydı</span>
              <Badge variant="outline">Ctrl+S</Badge>
            </div>
            <div className="flex justify-between">
              <span>Günlük rapor</span>
              <Badge variant="outline">Ctrl+Alt+D</Badge>
            </div>
            <div className="flex justify-between">
              <span>İhlal raporu</span>
              <Badge variant="outline">Ctrl+Alt+V</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Düzenleme Kısayolları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Yeni hükümlü</span>
              <Badge variant="outline">Ctrl+N</Badge>
            </div>
            <div className="flex justify-between">
              <span>Kaydet</span>
              <Badge variant="outline">Ctrl+S</Badge>
            </div>
            <div className="flex justify-between">
              <span>İptal</span>
              <Badge variant="outline">Esc</Badge>
            </div>
            <div className="flex justify-between">
              <span>Sil</span>
              <Badge variant="outline">Delete</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Görünüm Kısayolları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Araç çubuğu</span>
              <Badge variant="outline">Ctrl+T</Badge>
            </div>
            <div className="flex justify-between">
              <span>Durum çubuğu</span>
              <Badge variant="outline">Ctrl+B</Badge>
            </div>
            <div className="flex justify-between">
              <span>Tam ekran</span>
              <Badge variant="outline">F11</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

const TroubleshootingSection = () => (
  <div className="space-y-6">
    <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-3">
      <ExclamationTriangleIcon className="w-7 h-7 text-orange-600" />
      Sorun Giderme
    </h2>

    <Card>
      <CardHeader>
        <CardTitle>Sık Karşılaşılan Sorunlar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-red-600">Giriş yapamıyorum</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
              <li>Kullanıcı adı ve şifrenizi kontrol edin</li>
              <li>Caps Lock tuşunun durumunu kontrol edin</li>
              <li>Yöneticinizle iletişime geçin</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-red-600">Sayfa yavaş yükleniyor</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
              <li>İnternet bağlantınızı kontrol edin</li>
              <li>Tarayıcı önbelleğini temizleyin</li>
              <li>Sayfayı yenileyin (F5)</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-red-600">Excel dosyası içe aktarılamıyor</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
              <li>Dosya formatının doğru olduğunu kontrol edin</li>
              <li>Gerekli sütunların mevcut olduğundan emin olun</li>
              <li>TC Kimlik No formatını kontrol edin</li>
              <li>Dosya boyutunun limitler dahilinde olduğunu kontrol edin</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-red-600">Rapor oluşturulmuyor</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
              <li>Tarih aralığının doğru seçildiğini kontrol edin</li>
              <li>Yeterli veri bulunduğundan emin olun</li>
              <li>Sayfayı yenileyip tekrar deneyin</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Performans Optimizasyonu</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Tarayıcı Güncellemeleri:</strong> En son tarayıcı sürümünü kullanın</p>
          <p>• <strong>Önbellek Temizleme:</strong> Düzenli olarak tarayıcı önbelleğini temizleyin</p>
          <p>• <strong>Gereksiz Sekmeleri Kapatın:</strong> Sistemin performansını artırır</p>
          <p>• <strong>Büyük Rapor Dikkat:</strong> Çok büyük tarih aralıkları performansı etkileyebilir</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Teknik Destek</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• Sorun devam ederse sistem yöneticinizle iletişime geçin</p>
          <p>• Hata mesajının tam metnini not alın</p>
          <p>• Hatanın oluştuğu sayfa ve işlemi belirtin</p>
          <p>• Sistem loglarını kontrol etmek için yönetici desteği alın</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Veri Güvenliği İpuçları</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>• <strong>Düzenli Yedekleme:</strong> Önemli işlemlerden önce veri yedeği alın</p>
          <p>• <strong>Güçlü Şifre:</strong> Karmaşık ve benzersiz şifreler kullanın</p>
          <p>• <strong>Oturum Güvenliği:</strong> İş bitiminde oturumunuzu kapatın</p>
          <p>• <strong>Yetkisiz Erişim:</strong> Şifrenizi başkalarıyla paylaşmayın</p>
        </div>
      </CardContent>
    </Card>
  </div>
);

export default UsageGuidePage;
