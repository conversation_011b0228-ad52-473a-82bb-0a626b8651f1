// Test signature period grouping functionality

const testPeriods = [
  {
    id: 1,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'TUESDAY',
    time_start: '09:00',
    time_end: '17:00',
    is_active: true
  },
  {
    id: 2,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'THURSDAY',
    time_start: '09:00',
    time_end: '17:00',
    is_active: true
  },
  {
    id: 3,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'SATURDAY',
    time_start: '09:00',
    time_end: '17:00',
    is_active: true
  },
  {
    id: 4,
    start_date: '2025-08-03',
    end_date: '2025-12-31',
    frequency_type: 'MONTHLY_SPECIFIC',
    frequency_value: '1,15,30',
    time_start: '09:00',
    time_end: '17:00',
    is_active: true
  }
];

// Simulate grouping function
function groupSignaturePeriods(periods) {
  const groups = new Map();
  
  // Group periods by date range, type, and time
  periods.forEach(period => {
    const key = `${period.start_date}_${period.end_date}_${period.frequency_type}_${period.time_start || ''}_${period.time_end || ''}`;
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(period);
  });
  
  // Convert groups to consolidated periods
  const groupedPeriods = Array.from(groups.values()).map(groupPeriods => {
    // Use the first period as base
    const basePeriod = groupPeriods[0];
    
    // Merge frequency values based on type
    let mergedFrequencyValue = '';
    if (basePeriod.frequency_type === 'WEEKLY') {
      // Collect all weekly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      mergedFrequencyValue = Array.from(allDays).join(',');
    } else if (basePeriod.frequency_type === 'MONTHLY_SPECIFIC') {
      // Collect all monthly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      // Sort numerically for monthly days
      const sortedDays = Array.from(allDays).sort((a, b) => parseInt(a) - parseInt(b));
      mergedFrequencyValue = sortedDays.join(',');
    } else {
      // For X_DAYS type, use the first period's value
      mergedFrequencyValue = basePeriod.frequency_value;
    }
    
    // Return a merged period
    return {
      ...basePeriod,
      frequency_value: mergedFrequencyValue
    };
  });
  
  return groupedPeriods;
}

// Day names mapping
const dayNamesTurkish = {
  'MONDAY': 'Pazartesi',
  'TUESDAY': 'Salı',
  'WEDNESDAY': 'Çarşamba',
  'THURSDAY': 'Perşembe',
  'FRIDAY': 'Cuma',
  'SATURDAY': 'Cumartesi',
  'SUNDAY': 'Pazar'
};

function formatPeriodDisplay(period) {
  let frequency = '';
  let days = '';
  let time = 'Saat 09:00 - 17:00';

  if (period.time_start && period.time_end) {
    time = `Saat ${period.time_start} - ${period.time_end}`;
  }

  switch (period.frequency_type) {
    case 'WEEKLY': {
      const weeklyDays = (period.frequency_value || '').split(',').map(day => day.trim());
      const translatedDays = weeklyDays.map(day => dayNamesTurkish[day] || day).filter(Boolean);
      frequency = `Haftada ${translatedDays.length} Gün`;
      days = translatedDays.join(', ');
      break;
    }
    case 'X_DAYS': {
      const xDaysNum = parseInt(period.frequency_value || '1');
      frequency = `${xDaysNum} Günde Bir`;
      days = 'Belirli aralıklarla';
      break;
    }
    case 'MONTHLY_SPECIFIC': {
      const monthlyDays = (period.frequency_value || '').split(',').map(day => day.trim()).filter(Boolean);
      frequency = `Ayda ${monthlyDays.length} Gün`;
      days = monthlyDays.join(', ') + '. günlerde';
      break;
    }
  }

  return { frequency, days, time };
}

console.log('🧪 Signature Period Grouping Test');
console.log('================================');

console.log('\n📅 Original Periods:');
testPeriods.forEach((period, index) => {
  console.log(`${index + 1}. ${period.start_date} - ${period.end_date}`);
  console.log(`   Type: ${period.frequency_type}, Value: ${period.frequency_value}`);
  console.log(`   Time: ${period.time_start} - ${period.time_end}`);
  console.log('');
});

console.log('🔄 Grouping Periods...\n');
const groupedPeriods = groupSignaturePeriods(testPeriods);

console.log('📋 Grouped Periods:');
groupedPeriods.forEach((period, index) => {
  const { frequency, days, time } = formatPeriodDisplay(period);
  console.log(`${index + 1}. ${period.start_date} - ${period.end_date}`);
  console.log(`   ${frequency}`);
  console.log(`   ${days}`);
  console.log(`   ${time}`);
  console.log('');
});

console.log('✅ Test completed!');
console.log(`📊 Original periods: ${testPeriods.length}, Grouped periods: ${groupedPeriods.length}`);
