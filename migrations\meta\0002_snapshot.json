{"version": "6", "dialect": "sqlite", "id": "2a87ae08-bd50-4103-8e63-58dac858154b", "prevId": "d3cdfb19-d1e3-452c-8fac-8bcf7529fe38", "tables": {"convicts": {"name": "convicts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "tc_no": {"name": "tc_no", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "supervision_start_date": {"name": "supervision_start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "supervision_end_date": {"name": "supervision_end_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "relative_phone_number": {"name": "relative_phone_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_number": {"name": "file_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"convicts_tc_no_unique": {"name": "convicts_tc_no_unique", "columns": ["tc_no"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "exemptions": {"name": "exemptions", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "convict_id": {"name": "convict_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "exemption_type": {"name": "exemption_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "document_path": {"name": "document_path", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"exemptions_convict_id_convicts_id_fk": {"name": "exemptions_convict_id_convicts_id_fk", "tableFrom": "exemptions", "tableTo": "convicts", "columnsFrom": ["convict_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "exemptions_created_by_users_id_fk": {"name": "exemptions_created_by_users_id_fk", "tableFrom": "exemptions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "signature_periods": {"name": "signature_periods", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "convict_id": {"name": "convict_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "frequency_type": {"name": "frequency_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "frequency_value": {"name": "frequency_value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reference_date": {"name": "reference_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "time_start": {"name": "time_start", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "time_end": {"name": "time_end", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "allowed_days": {"name": "allowed_days", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"signature_periods_convict_id_convicts_id_fk": {"name": "signature_periods_convict_id_convicts_id_fk", "tableFrom": "signature_periods", "tableTo": "convicts", "columnsFrom": ["convict_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "signatures": {"name": "signatures", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "convict_id": {"name": "convict_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "signature_date": {"name": "signature_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "signature_time": {"name": "signature_time", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "recorded_by": {"name": "recorded_by", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"signatures_convict_id_convicts_id_fk": {"name": "signatures_convict_id_convicts_id_fk", "tableFrom": "signatures", "tableTo": "convicts", "columnsFrom": ["convict_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "signatures_recorded_by_users_id_fk": {"name": "signatures_recorded_by_users_id_fk", "tableFrom": "signatures", "tableTo": "users", "columnsFrom": ["recorded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}